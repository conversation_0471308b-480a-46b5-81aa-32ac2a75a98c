# MIT CVD App Environment Variables
# Copy this file to .env and fill in your values

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Organization ID if using OpenAI organization
# OPENAI_ORG_ID=your_org_id_here

# App Configuration
APP_NAME=MIT_CVD_APP
DEBUG=True

# Directory Configuration (relative to project root)
DATA_DIR=data
REPORTS_DIR=reports
TEMP_DIR=temp

# Iris Database Configuration
IRISINSTALLDIR="/usr"
INTERSYSTEM_USERNAME="demo"
INTERSYSTEM_PASSWORD="demo"
IRIS_HOSTNAME="localhost"
IRIS_PORT="1972"
IRIS_NAMESPACE="USER"
