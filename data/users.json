[{"id": "d81b926c-2c40-4538-9f2a-1f84a84765d7", "name": "<PERSON>", "age": 52, "email": "<EMAIL>", "created_at": "2025-09-06T19:16:04.317101", "updated_at": "2025-09-07T12:01:03.460930", "profile": {"lifestyle": {"exercise_frequency": 5, "sleep_hours": 8, "stress_level": 3, "social_support": 9, "work_life_balance": 8}, "family_history": {"heart_attack": true, "stroke": false, "high_cholesterol": true, "diabetes": false, "hypertension": true}, "clinical": {"total_cholesterol": 220, "hdl_cholesterol": 40, "systolic_blood_pressure": 140, "hypertension_treatment": true, "diabetes": false, "bmi": 28.5, "waist_circumference": 95, "last_physical_exam": "2024-12-01"}}, "cvd_scores": [{"id": "88a3c47a-9695-4573-999d-81be13435912", "user_id": "d81b926c-2c40-4538-9f2a-1f84a84765d7", "score": 0.44499999999999995, "risk_level": "Moderate", "factors": {"age_risk": 0.5, "family_risk": 1.0, "lifestyle_risk": 0.1, "nutrition_risk": 0.5, "risk_factors": ["Family history of heart attack", "Family history of high blood pressure"], "protective_factors": ["Active lifestyle (3+ workouts/week)", "Non-smoker", "Healthy sleep patterns"]}, "calculated_at": "2025-09-06T19:16:04.317763"}, {"id": "def9215f-f26f-4c6f-98c6-521d2bc3e785", "user_id": "d81b926c-2c40-4538-9f2a-1f84a84765d7", "score": 0.44499999999999995, "risk_level": "Moderate", "factors": {"age_risk": 0.5, "family_risk": 1.0, "lifestyle_risk": 0.1, "nutrition_risk": 0.5, "risk_factors": ["Family history of heart attack", "Family history of high blood pressure"], "protective_factors": ["Active lifestyle (3+ workouts/week)", "Non-smoker", "Healthy sleep patterns"]}, "calculated_at": "2025-09-06T19:18:04.769594"}, {"id": "6cbb7f1f-73de-4a1c-862e-21001731c22c", "user_id": "d81b926c-2c40-4538-9f2a-1f84a84765d7", "score": 0.44499999999999995, "risk_level": "Moderate", "factors": {"age_risk": 0.5, "family_risk": 1.0, "lifestyle_risk": 0.1, "nutrition_risk": 0.5, "risk_factors": ["Family history of heart attack", "Family history of high blood pressure"], "protective_factors": ["Active lifestyle (3+ workouts/week)", "Non-smoker", "Healthy sleep patterns"]}, "calculated_at": "2025-09-06T19:40:38.242897"}, {"id": "1bab619e-015d-4b30-a836-4aa4cacd1794", "user_id": "d81b926c-2c40-4538-9f2a-1f84a84765d7", "score": 0.2419664200251381, "risk_level": "High", "factors": {"framingham_risk": 0.14477628811197074, "family_adjusted_risk": 0.18097036013996343, "lifestyle_adjusted_risk": 0.2419664200251381, "adjusted_risk": 0.18097036013996343, "risk_category": {"name": "high", "label": "High", "description": "≥20% 10-year risk", "threshold": 1.0}, "clinical_parameters": {"age": 52, "gender": "male", "total_cholesterol": 220, "hdl_cholesterol": 40, "systolic_bp": 140, "on_bp_medication": true, "smoking": false, "diabetes": false}, "risk_factors": ["Age 52 (men ≥45 years at increased risk)", "Borderline high cholesterol (220 mg/dL)", "High blood pressure (140 mmHg)", "Currently on blood pressure medication", "Family history of hypertension"], "protective_factors": ["Non-smoker"]}, "calculated_at": "2025-09-07T11:45:09.443654"}, {"id": "ebaf4d46-537c-4b63-b36c-e1369294d85f", "user_id": "d81b926c-2c40-4538-9f2a-1f84a84765d7", "score": 0.07288988437947343, "risk_level": "Borderline", "factors": {"framingham_risk": 0.14477628811197074, "family_adjusted_risk": 0.18097036013996343, "lifestyle_adjusted_risk": 0.07288988437947343, "adjusted_risk": 0.18097036013996343, "risk_category": {"name": "borderline", "label": "Borderline", "description": "5-7.4% 10-year risk", "threshold": 0.075}, "clinical_parameters": {"age": 52, "gender": "male", "total_cholesterol": 220, "hdl_cholesterol": 40, "systolic_bp": 140, "on_bp_medication": true, "smoking": false, "diabetes": false}, "risk_factors": ["Age 52 (men ≥45 years at increased risk)", "Borderline high cholesterol (220 mg/dL)", "High blood pressure (140 mmHg)", "Currently on blood pressure medication", "Family history of hypertension"], "protective_factors": ["Very active lifestyle (5+ workouts/week)", "Non-smoker", "Healthy sleep patterns", "Low stress levels"]}, "calculated_at": "2025-09-07T12:01:03.460636"}], "nutrition_logs": [{"name": "Grilled salmon with vegetables", "nutrition": {"calories": 350, "saturated_fat": 2.5, "trans_fat": 0, "sodium": 180, "fiber": 4, "sugar": 6, "protein": 28}, "image_path": "demo_food.jpg"}, {"name": "Grilled salmon with vegetables", "nutrition": {"calories": 350, "saturated_fat": 2.5, "trans_fat": 0, "sodium": 180, "fiber": 4, "sugar": 6, "protein": 28}, "image_path": "demo_food.jpg"}, {"name": "Grilled salmon with vegetables", "nutrition": {"calories": 350, "saturated_fat": 2.5, "trans_fat": 0, "sodium": 180, "fiber": 4, "sugar": 6, "protein": 28}, "image_path": "demo_food.jpg"}], "chat_sessions": [], "gender": "male"}, {"id": "c177ab81-5c68-42c7-904d-a4a835254d8c", "name": "Test User", "age": 35, "email": "<EMAIL>", "created_at": "2025-09-06T20:14:29.146846", "updated_at": "2025-09-06T20:19:27.456742", "profile": {"lifestyle": {"exercise_frequency": 5}}, "cvd_scores": [], "nutrition_logs": [], "chat_sessions": []}, {"id": "76236dc1-c98e-4473-9f05-7d45046c6932", "name": "Session Test User", "age": 30, "email": "<EMAIL>", "created_at": "2025-09-06T20:26:34.912340", "updated_at": "2025-09-06T20:26:40.083305", "profile": {"family_history": {}, "medical_conditions": [], "medications": [], "lifestyle": {"smoking": false, "exercise_frequency": 0, "stress_level": 5, "sleep_hours": 8}}, "cvd_scores": [], "nutrition_logs": [], "chat_sessions": ["73163964-8f13-4a69-be19-e8ebd01a2077"]}, {"id": "f7d16c46-4afd-4477-aa14-46c2fb0fcbd5", "username": "le<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "password": "289160db0d9f39f9ae1754c4ec9c16f90b50e32e09c5fb5481ae642b3d3d1a36", "name": "Leonardo", "created_at": "2025-09-07T02:08:11.159531", "updated_at": "2025-09-07T02:08:11.159574", "profile": {"lifestyle": {"smoking": false, "exercise_frequency": 0, "stress_level": 5, "sleep_hours": 8}, "family_history": {"heart_attack": false, "high_blood_pressure": false}}, "cvd_scores": [{"id": "f55ca3b3-6b7d-46fa-9a6a-93883e98a582", "user_id": "f7d16c46-4afd-4477-aa14-46c2fb0fcbd5", "score": 0.245, "risk_level": "Low", "factors": {"age_risk": 0.3, "family_risk": 0.0, "lifestyle_risk": 0.3, "nutrition_risk": 0.5, "risk_factors": ["Sedentary lifestyle"], "protective_factors": ["Non-smoker", "Healthy sleep patterns", "Young age"]}, "calculated_at": "2025-09-07T03:15:27.259513"}, {"id": "759921e4-ca16-4412-a7e2-5e378e3a4779", "user_id": "f7d16c46-4afd-4477-aa14-46c2fb0fcbd5", "score": 0.245, "risk_level": "Low", "factors": {"age_risk": 0.3, "family_risk": 0.0, "lifestyle_risk": 0.3, "nutrition_risk": 0.5, "risk_factors": ["Sedentary lifestyle"], "protective_factors": ["Non-smoker", "Healthy sleep patterns", "Young age"]}, "calculated_at": "2025-09-07T03:15:37.711468"}], "nutrition_logs": [{"timestamp": "2025-09-07T03:15:27.253042", "food_name": "Unknown food", "nutrition": {}, "heart_health_score": 5.0, "cvd_risk_factors": []}, {"timestamp": "2025-09-07T03:15:37.701356", "food_name": "Unknown food", "nutrition": {}, "heart_health_score": 5.0, "cvd_risk_factors": []}]}, {"id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "username": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "password": "d8b900b4ad99c236de60c0a8e7ed47d5d69be46adcf3f7b99293c2c9e62b428d", "name": "<PERSON><PERSON>", "created_at": "2025-09-07T08:35:18.273598", "updated_at": "2025-09-07T11:38:07.147919", "profile": {"lifestyle": {"smoking": true, "exercise_frequency": 2, "sleep_hours": 4.0, "stress_level": 10, "social_support": 10, "work_life_balance": 6, "alcohol_consumption": "Heavy", "diet_type": "Vegan"}, "family_history": {"heart_attack": true, "high_blood_pressure": true, "stroke": true, "diabetes": true, "high_cholesterol": true, "heart_attack_early_onset": true, "stroke_early_onset": true, "diabetes_early_onset": true, "heart_attack_multiple": true, "high_blood_pressure_multiple": true, "diabetes_multiple": true}, "medical_conditions": ["Heart Disease"], "medications": [], "clinical": {"total_cholesterol": 500.0, "hdl_cholesterol": 20.0, "systolic_blood_pressure": 80.0, "hypertension_treatment": true, "diabetes": true}}, "cvd_scores": [{"id": "235e9f49-98e3-45d1-9bd0-fce527f82fd6", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.245, "risk_level": "Low", "factors": {"age_risk": 0.3, "family_risk": 0.0, "lifestyle_risk": 0.3, "nutrition_risk": 0.5, "risk_factors": ["Sedentary lifestyle"], "protective_factors": ["Non-smoker", "Healthy sleep patterns", "Young age"]}, "calculated_at": "2025-09-07T09:42:34.544158"}, {"id": "2dab983b-123c-4ebd-a8bb-8e892719f638", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.445, "risk_level": "Moderate", "factors": {"age_risk": 0.3, "family_risk": 0.8, "lifestyle_risk": 0.3, "nutrition_risk": 0.5, "risk_factors": ["Family history of diabetes", "Family history of high cholesterol", "Sedentary lifestyle"], "protective_factors": ["Non-smoker", "Healthy sleep patterns", "Young age"]}, "calculated_at": "2025-09-07T09:43:25.544218"}, {"id": "198abeec-abce-4da0-b927-f1e1b00cb37a", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.445, "risk_level": "Moderate", "factors": {"age_risk": 0.3, "family_risk": 0.8, "lifestyle_risk": 0.3, "nutrition_risk": 0.5, "risk_factors": ["Family history of diabetes", "Family history of high cholesterol", "Sedentary lifestyle"], "protective_factors": ["Non-smoker", "Healthy sleep patterns", "Young age"]}, "calculated_at": "2025-09-07T09:44:29.913808"}, {"id": "e9e24a05-6095-4556-bedf-6a3a78aadab0", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.445, "risk_level": "Moderate", "factors": {"age_risk": 0.3, "family_risk": 0.8, "lifestyle_risk": 0.3, "nutrition_risk": 0.5, "risk_factors": ["Family history of diabetes", "Family history of high cholesterol", "Sedentary lifestyle"], "protective_factors": ["Non-smoker", "Healthy sleep patterns", "Young age"]}, "calculated_at": "2025-09-07T09:44:37.680876"}, {"id": "f94b7f06-473f-4046-b0c1-7a74497bb014", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.76, "risk_level": "High", "factors": {"age_risk": 0.3, "family_risk": 0.8, "lifestyle_risk": 0.9999999999999999, "nutrition_risk": 0.5, "risk_factors": ["Family history of diabetes", "Family history of high cholesterol", "Current smoker", "Sedentary lifestyle", "High stress levels", "Insufficient sleep"], "protective_factors": ["Young age"]}, "calculated_at": "2025-09-07T09:45:09.485367"}, {"id": "277f52d4-c529-4f18-b05f-7314c760fd74", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.72, "risk_level": "High", "factors": {"age_risk": 0.1, "family_risk": 0.8, "lifestyle_risk": 0.9999999999999999, "nutrition_risk": 0.5, "risk_factors": ["Family history of diabetes", "Family history of high cholesterol", "Current smoker", "Sedentary lifestyle", "High stress levels", "Insufficient sleep"], "protective_factors": ["Young age"]}, "calculated_at": "2025-09-07T09:45:32.553897"}, {"id": "515acec0-3b50-4164-8132-e5c7c0e14c0f", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.9, "risk_level": "Very High", "factors": {"age_risk": 1.0, "family_risk": 0.8, "lifestyle_risk": 0.9999999999999999, "nutrition_risk": 0.5, "risk_factors": ["Age 80 (increased risk)", "Family history of diabetes", "Family history of high cholesterol", "Current smoker", "Sedentary lifestyle", "High stress levels", "Insufficient sleep"], "protective_factors": []}, "calculated_at": "2025-09-07T09:45:39.015851"}, {"id": "00b4f701-bb84-45ad-a5d8-db49d75666dd", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.9, "risk_level": "Very High", "factors": {"age_risk": 1.0, "family_risk": 0.8, "lifestyle_risk": 0.9999999999999999, "nutrition_risk": 0.5, "risk_factors": ["Age 73 (increased risk)", "Family history of diabetes", "Family history of high cholesterol", "Current smoker", "Sedentary lifestyle", "High stress levels", "Insufficient sleep"], "protective_factors": []}, "calculated_at": "2025-09-07T09:51:29.609453"}, {"id": "b87cf949-faf4-4897-8720-31577873fef4", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.95, "risk_level": "Very High", "factors": {"age_risk": 1.0, "family_risk": 1.0, "lifestyle_risk": 0.9999999999999999, "nutrition_risk": 0.5, "risk_factors": ["Age 73 (increased risk)", "Family history of heart attack", "Family history of stroke", "Family history of high blood pressure", "Family history of diabetes", "Family history of high cholesterol", "Current smoker", "Sedentary lifestyle", "High stress levels", "Insufficient sleep"], "protective_factors": []}, "calculated_at": "2025-09-07T09:51:35.404572"}, {"id": "aaf84eb6-5837-42a3-86db-b0ecd2ac6953", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.95, "risk_level": "Very High", "factors": {"age_risk": 1.0, "family_risk": 1.0, "lifestyle_risk": 0.9999999999999999, "nutrition_risk": 0.5, "risk_factors": ["Age 73 (increased risk)", "Family history of heart attack", "Family history of stroke", "Family history of high blood pressure", "Family history of diabetes", "Family history of high cholesterol", "Current smoker", "Sedentary lifestyle", "High stress levels", "Insufficient sleep"], "protective_factors": []}, "calculated_at": "2025-09-07T09:51:45.642096"}, {"id": "c42cf12f-4bf1-4703-a8e9-68ea1a4d3ddc", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.95, "risk_level": "Very High", "factors": {"age_risk": 1.0, "family_risk": 1.0, "lifestyle_risk": 0.9999999999999999, "nutrition_risk": 0.5, "risk_factors": ["Age 73 (increased risk)", "Family history of heart attack", "Family history of stroke", "Family history of high blood pressure", "Family history of diabetes", "Family history of high cholesterol", "Current smoker", "Sedentary lifestyle", "High stress levels", "Insufficient sleep"], "protective_factors": []}, "calculated_at": "2025-09-07T09:51:51.315264"}, {"id": "3e415bfe-4d04-4047-85b0-ddb8e3bede88", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.95, "risk_level": "Very High", "factors": {"age_risk": 1.0, "family_risk": 1.0, "lifestyle_risk": 0.9999999999999999, "nutrition_risk": 0.5, "risk_factors": ["Age 73 (increased risk)", "Family history of heart attack", "Family history of stroke", "Family history of high blood pressure", "Family history of diabetes", "Family history of high cholesterol", "Current smoker", "Sedentary lifestyle", "High stress levels", "Excessive sleep"], "protective_factors": []}, "calculated_at": "2025-09-07T09:52:05.513341"}, {"id": "60f56354-9fbb-40d3-bfff-f6ce7714e5db", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.95, "risk_level": "Very High", "factors": {"age_risk": 1.0, "family_risk": 1.0, "lifestyle_risk": 0.9999999999999999, "nutrition_risk": 0.5, "risk_factors": ["Age 73 (increased risk)", "Family history of heart attack", "Family history of stroke", "Family history of high blood pressure", "Family history of diabetes", "Family history of high cholesterol", "Current smoker", "Sedentary lifestyle", "High stress levels", "Excessive sleep"], "protective_factors": []}, "calculated_at": "2025-09-07T09:52:11.561862"}, {"id": "e929f886-40a4-4bef-b503-6658a8971993", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.95, "risk_level": "Very High", "factors": {"age_risk": 1.0, "family_risk": 1.0, "lifestyle_risk": 0.9999999999999999, "nutrition_risk": 0.5, "risk_factors": ["Age 73 (increased risk)", "Family history of heart attack", "Family history of stroke", "Family history of high blood pressure", "Family history of diabetes", "Family history of high cholesterol", "Current smoker", "Sedentary lifestyle", "High stress levels", "Excessive sleep"], "protective_factors": []}, "calculated_at": "2025-09-07T09:52:22.317992"}, {"id": "68ed25ab-e9ca-4530-8158-5a76b7301556", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.021318163845730598, "risk_level": "Low", "factors": {"framingham_risk": 0.021318163845730598, "adjusted_risk": 0.021318163845730598, "risk_category": {"name": "low", "label": "Low", "description": "<5% 10-year risk", "threshold": 0.05}, "clinical_parameters": {"age": 30, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": false, "diabetes": false}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)"], "protective_factors": ["Non-smoker", "Healthy sleep patterns", "Low stress levels", "Young age"]}, "calculated_at": "2025-09-07T11:22:32.287825"}, {"id": "0ff1301e-1c6e-4be9-9593-42ab6b38311a", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.021318163845730598, "risk_level": "Low", "factors": {"framingham_risk": 0.021318163845730598, "adjusted_risk": 0.021318163845730598, "risk_category": {"name": "low", "label": "Low", "description": "<5% 10-year risk", "threshold": 0.05}, "clinical_parameters": {"age": 30, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": false, "diabetes": false}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)"], "protective_factors": ["Non-smoker", "Low stress levels", "Young age"]}, "calculated_at": "2025-09-07T11:22:42.934116"}, {"id": "ee9c5ee5-fd94-4017-bcb3-dff68c0c4549", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.021318163845730598, "risk_level": "Low", "factors": {"framingham_risk": 0.021318163845730598, "adjusted_risk": 0.021318163845730598, "risk_category": {"name": "low", "label": "Low", "description": "<5% 10-year risk", "threshold": 0.05}, "clinical_parameters": {"age": 30, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": false, "diabetes": false}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)"], "protective_factors": ["Non-smoker", "Young age"]}, "calculated_at": "2025-09-07T11:22:46.321186"}, {"id": "b3ca1a1a-5f7f-4e5a-85cf-60278fa2a00c", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.04061608300387487, "risk_level": "Low", "factors": {"framingham_risk": 0.04061608300387487, "adjusted_risk": 0.04061608300387487, "risk_category": {"name": "low", "label": "Low", "description": "<5% 10-year risk", "threshold": 0.05}, "clinical_parameters": {"age": 30, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": true, "diabetes": false}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Current smoker (major modifiable risk factor)"], "protective_factors": ["Young age"]}, "calculated_at": "2025-09-07T11:22:51.245698"}, {"id": "958286c6-52a6-4eea-bc84-ebeeb277f98c", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.07094646880119793, "risk_level": "Borderline", "factors": {"framingham_risk": 0.07094646880119793, "adjusted_risk": 0.07094646880119793, "risk_category": {"name": "borderline", "label": "Borderline", "description": "5-7.4% 10-year risk", "threshold": 0.075}, "clinical_parameters": {"age": 30, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": true, "diabetes": true}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Current smoker (major modifiable risk factor)", "Diabetes mellitus"], "protective_factors": ["Young age"]}, "calculated_at": "2025-09-07T11:23:47.311340"}, {"id": "6afb1fa2-3050-4ea4-9cd8-87dff0694642", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.05229177760058734, "risk_level": "Borderline", "factors": {"framingham_risk": 0.05229177760058734, "adjusted_risk": 0.05229177760058734, "risk_category": {"name": "borderline", "label": "Borderline", "description": "5-7.4% 10-year risk", "threshold": 0.075}, "clinical_parameters": {"age": 30, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": true, "smoking": true, "diabetes": true}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Currently on blood pressure medication", "Current smoker (major modifiable risk factor)", "Diabetes mellitus"], "protective_factors": ["Young age"]}, "calculated_at": "2025-09-07T11:23:52.866812"}, {"id": "35897d09-0c24-494a-8ebd-7c03c68ebf54", "user_id": "b12cd4cb-5b51-497b-ba52-768226f776a7", "score": 0.05229177760058734, "risk_level": "Borderline", "factors": {"framingham_risk": 0.05229177760058734, "adjusted_risk": 0.05229177760058734, "risk_category": {"name": "borderline", "label": "Borderline", "description": "5-7.4% 10-year risk", "threshold": 0.075}, "clinical_parameters": {"age": 30, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": true, "smoking": true, "diabetes": true}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Currently on blood pressure medication", "Current smoker (major modifiable risk factor)", "Diabetes mellitus"], "protective_factors": ["Young age"]}, "calculated_at": "2025-09-07T11:23:58.770189"}], "age": 30, "gender": "male"}, {"name": "Test User", "age": 30, "id": "test_user_id", "profile": {}, "nutrition_logs": [{"timestamp": "2025-09-07T08:49:56.332761", "food_name": "Grilled Salmon with Asparagus, Cherry Tomatoes, and Lemon", "nutrition": {"calories": 350, "saturated_fat": 3.0, "trans_fat": 0.0, "sodium": 90, "fiber": 3.5, "sugar": 3.0, "protein": 32.0}, "heart_health_score": 9.0, "cvd_risk_factors": ["Contains some saturated fat (from salmon)", "Potentially low fiber if portion of vegetables is small"]}], "cvd_scores": [{"id": "d851d2b9-fc95-41d9-90f0-548f05f1fc6e", "user_id": "test_user_id", "score": 0.245, "risk_level": "Low", "factors": {"age_risk": 0.3, "family_risk": 0.0, "lifestyle_risk": 0.3, "nutrition_risk": 0.5, "risk_factors": ["Sedentary lifestyle"], "protective_factors": ["Non-smoker", "Healthy sleep patterns", "Young age"]}, "calculated_at": "2025-09-07T08:49:56.350602"}]}, {"id": "456b9abc-16c5-4056-a2d9-43cfa48e3669", "username": "davi", "email": "<EMAIL>", "password": "d8b900b4ad99c236de60c0a8e7ed47d5d69be46adcf3f7b99293c2c9e62b428d", "name": "<PERSON><PERSON>", "created_at": "2025-09-07T11:42:42.249649", "updated_at": "2025-09-07T12:01:53.699695", "profile": {"lifestyle": {"smoking": true, "exercise_frequency": 0, "sleep_hours": 8.0, "stress_level": 1, "social_support": 6, "work_life_balance": 3, "alcohol_consumption": "Heavy", "diet_type": "Standard"}, "family_history": {"heart_attack": true, "high_blood_pressure": true, "stroke": true, "diabetes": false, "high_cholesterol": false, "heart_attack_early_onset": false, "stroke_early_onset": false, "diabetes_early_onset": true, "heart_attack_multiple": false, "high_blood_pressure_multiple": false, "diabetes_multiple": true}, "clinical": {"total_cholesterol": 300.0, "hdl_cholesterol": 50.0, "systolic_blood_pressure": 120.0, "hypertension_treatment": false, "diabetes": true}, "medical_conditions": ["None"], "medications": []}, "cvd_scores": [{"id": "a7298236-c8ec-47cf-a82d-f257b20af437", "user_id": "456b9abc-16c5-4056-a2d9-43cfa48e3669", "score": 0.07813718351542787, "risk_level": "Intermediate", "factors": {"framingham_risk": 0.07813718351542787, "adjusted_risk": 0.07813718351542787, "risk_category": {"name": "intermediate", "label": "Intermediate", "description": "7.5-19.9% 10-year risk", "threshold": 0.2}, "clinical_parameters": {"age": 31, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": true, "diabetes": true}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Current smoker (major modifiable risk factor)", "Diabetes mellitus"], "protective_factors": ["Healthy sleep patterns", "Young age"]}, "calculated_at": "2025-09-07T11:48:07.173384"}, {"id": "0cd41ff4-6942-46a4-a4fa-1edcf481373b", "user_id": "456b9abc-16c5-4056-a2d9-43cfa48e3669", "score": 0.10157833857005623, "risk_level": "Intermediate", "factors": {"framingham_risk": 0.07813718351542787, "adjusted_risk": 0.10157833857005623, "risk_category": {"name": "intermediate", "label": "Intermediate", "description": "7.5-19.9% 10-year risk", "threshold": 0.2}, "clinical_parameters": {"age": 31, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": true, "diabetes": true}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Current smoker (major modifiable risk factor)", "Diabetes mellitus", "Family history of stroke"], "protective_factors": ["Healthy sleep patterns", "Young age"]}, "calculated_at": "2025-09-07T11:48:32.591114"}, {"id": "375727ea-8779-49df-a9b5-c65f43746fd2", "user_id": "456b9abc-16c5-4056-a2d9-43cfa48e3669", "score": 0.10157833857005623, "risk_level": "Intermediate", "factors": {"framingham_risk": 0.07813718351542787, "adjusted_risk": 0.10157833857005623, "risk_category": {"name": "intermediate", "label": "Intermediate", "description": "7.5-19.9% 10-year risk", "threshold": 0.2}, "clinical_parameters": {"age": 31, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": true, "diabetes": true}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Current smoker (major modifiable risk factor)", "Diabetes mellitus", "Family history of stroke"], "protective_factors": ["Healthy sleep patterns", "Low stress levels", "Young age"]}, "calculated_at": "2025-09-07T11:48:37.952566"}, {"id": "10b1cd6c-5020-4540-814a-fa697c4484ec", "user_id": "456b9abc-16c5-4056-a2d9-43cfa48e3669", "score": 0.10157833857005623, "risk_level": "Intermediate", "factors": {"framingham_risk": 0.07813718351542787, "adjusted_risk": 0.10157833857005623, "risk_category": {"name": "intermediate", "label": "Intermediate", "description": "7.5-19.9% 10-year risk", "threshold": 0.2}, "clinical_parameters": {"age": 31, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": true, "diabetes": true}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Current smoker (major modifiable risk factor)", "Diabetes mellitus", "Family history of stroke"], "protective_factors": ["Healthy sleep patterns", "Low stress levels", "Young age"]}, "calculated_at": "2025-09-07T11:48:48.632059"}, {"id": "3efff474-1dfd-4341-87fe-0fed7aaa8747", "user_id": "456b9abc-16c5-4056-a2d9-43cfa48e3669", "score": 0.10157833857005623, "risk_level": "Intermediate", "factors": {"framingham_risk": 0.07813718351542787, "adjusted_risk": 0.10157833857005623, "risk_category": {"name": "intermediate", "label": "Intermediate", "description": "7.5-19.9% 10-year risk", "threshold": 0.2}, "clinical_parameters": {"age": 31, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": true, "diabetes": true}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Current smoker (major modifiable risk factor)", "Diabetes mellitus", "Family history of stroke"], "protective_factors": ["Healthy sleep patterns", "Low stress levels", "Young age"]}, "calculated_at": "2025-09-07T11:48:50.336722"}, {"id": "e4f13007-f093-417a-90d7-c84ffec5f486", "user_id": "456b9abc-16c5-4056-a2d9-43cfa48e3669", "score": 0.10157833857005623, "risk_level": "Intermediate", "factors": {"framingham_risk": 0.07813718351542787, "adjusted_risk": 0.10157833857005623, "risk_category": {"name": "intermediate", "label": "Intermediate", "description": "7.5-19.9% 10-year risk", "threshold": 0.2}, "clinical_parameters": {"age": 31, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": true, "diabetes": true}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Current smoker (major modifiable risk factor)", "Diabetes mellitus", "Family history of stroke"], "protective_factors": ["Healthy sleep patterns", "Low stress levels", "Young age"]}, "calculated_at": "2025-09-07T11:50:24.685398"}, {"id": "66d86a42-9e56-4473-bfc4-635e9d3b14ab", "user_id": "456b9abc-16c5-4056-a2d9-43cfa48e3669", "score": 0.10157833857005623, "risk_level": "Intermediate", "factors": {"framingham_risk": 0.07813718351542787, "adjusted_risk": 0.10157833857005623, "risk_category": {"name": "intermediate", "label": "Intermediate", "description": "7.5-19.9% 10-year risk", "threshold": 0.2}, "clinical_parameters": {"age": 31, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": true, "diabetes": true}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Current smoker (major modifiable risk factor)", "Diabetes mellitus", "Family history of stroke"], "protective_factors": ["Healthy sleep patterns", "Low stress levels", "Young age"]}, "calculated_at": "2025-09-07T12:01:18.905113"}, {"id": "9327617c-938a-4256-9040-e1f722bb8875", "user_id": "456b9abc-16c5-4056-a2d9-43cfa48e3669", "score": 0.10157833857005623, "risk_level": "Intermediate", "factors": {"framingham_risk": 0.07813718351542787, "adjusted_risk": 0.10157833857005623, "risk_category": {"name": "intermediate", "label": "Intermediate", "description": "7.5-19.9% 10-year risk", "threshold": 0.2}, "clinical_parameters": {"age": 31, "gender": "male", "total_cholesterol": 200.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": true, "diabetes": true}, "risk_factors": ["Borderline high cholesterol (200.0 mg/dL)", "Current smoker (major modifiable risk factor)", "Diabetes mellitus", "Family history of stroke"], "protective_factors": ["Healthy sleep patterns", "Low stress levels", "Young age"]}, "calculated_at": "2025-09-07T12:01:44.891130"}, {"id": "f909fde5-d4e2-493e-8757-2143c8a0c739", "user_id": "456b9abc-16c5-4056-a2d9-43cfa48e3669", "score": 0.15655123898896853, "risk_level": "Intermediate", "factors": {"framingham_risk": 0.12042402999151425, "adjusted_risk": 0.15655123898896853, "risk_category": {"name": "intermediate", "label": "Intermediate", "description": "7.5-19.9% 10-year risk", "threshold": 0.2}, "clinical_parameters": {"age": 31, "gender": "male", "total_cholesterol": 300.0, "hdl_cholesterol": 50.0, "systolic_bp": 120.0, "on_bp_medication": false, "smoking": true, "diabetes": true}, "risk_factors": ["High total cholesterol (300.0 mg/dL, target <200)", "Current smoker (major modifiable risk factor)", "Diabetes mellitus", "Family history of stroke"], "protective_factors": ["Healthy sleep patterns", "Low stress levels", "Young age"]}, "calculated_at": "2025-09-07T12:01:53.699231"}], "age": 31, "gender": "male"}]