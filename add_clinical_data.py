#!/usr/bin/env python3
"""
Add clinical data to user for testing CVD calculation
"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from backend.modules.crud import list_users, update_user

def add_clinical_data():
    """Add clinical data to first user for testing"""
    print("🏥 ADDING CLINICAL DATA FOR TESTING")
    print("=" * 50)
    
    users = list_users()
    if not users:
        print("❌ No users found")
        return
    
    user = users[0]
    print(f"👤 Adding clinical data to: {user.get('name', 'Unknown')}")
    
    # Add comprehensive clinical data
    clinical_data = {
        'total_cholesterol': 220,  # mg/dL - slightly elevated
        'hdl_cholesterol': 40,     # mg/dL - low (risk factor)
        'systolic_blood_pressure': 140,  # mmHg - stage 1 hypertension
        'hypertension_treatment': True,  # on BP medication
        'diabetes': False,         # no diabetes
        'bmi': 28.5,              # overweight
        'waist_circumference': 95,  # cm - elevated for male
        'last_physical_exam': '2024-12-01'
    }
    
    # Add family history
    family_history = {
        'heart_attack': True,
        'stroke': False,
        'high_cholesterol': True,
        'diabetes': False,
        'hypertension': True
    }
    
    # Add lifestyle data
    lifestyle = {
        'exercise_frequency': 1,  # 1x per week - poor
        'sleep_hours': 6,         # insufficient sleep
        'stress_level': 8,        # high stress
        'social_support': 3,      # poor social support
        'work_life_balance': 2    # poor work-life balance
    }
    
    # Update user profile
    profile_updates = {
        'profile.clinical': clinical_data,
        'profile.family_history': family_history,
        'profile.lifestyle': lifestyle
    }
    
    # We need to update the nested structure properly
    current_profile = user.get('profile', {})
    current_profile['clinical'] = clinical_data
    current_profile['family_history'] = family_history
    current_profile['lifestyle'] = lifestyle
    
    update_data = {
        'profile': current_profile,
        'age': 52,  # Make sure age is set
        'gender': 'male'  # Make sure gender is set
    }
    
    success = update_user(user['id'], update_data)
    
    if success:
        print("✅ Clinical data added successfully")
        print(f"📊 Added data:")
        print(f"   Total Cholesterol: {clinical_data['total_cholesterol']} mg/dL")
        print(f"   HDL Cholesterol: {clinical_data['hdl_cholesterol']} mg/dL")
        print(f"   Systolic BP: {clinical_data['systolic_blood_pressure']} mmHg")
        print(f"   On BP Medication: {clinical_data['hypertension_treatment']}")
        print(f"   Diabetes: {clinical_data['diabetes']}")
        print(f"   Age: {update_data['age']}")
        print(f"   Gender: {update_data['gender']}")
        
        print(f"\n🧬 Family History:")
        for condition, value in family_history.items():
            if value:
                print(f"   ✅ {condition.replace('_', ' ').title()}")
        
        print(f"\n🏃‍♂️ Lifestyle (Risk Factors):")
        print(f"   Exercise: {lifestyle['exercise_frequency']}x/week")
        print(f"   Sleep: {lifestyle['sleep_hours']} hours")
        print(f"   Stress: {lifestyle['stress_level']}/10")
        print(f"   Social Support: {lifestyle['social_support']}/10")
        print(f"   Work-Life Balance: {lifestyle['work_life_balance']}/10")
        
        return True
    else:
        print("❌ Failed to update user")
        return False

if __name__ == "__main__":
    add_clinical_data()
