#!/usr/bin/env python3
"""
Test the comprehensive CVD risk calculation and breakdown display
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(__file__))

from backend.modules.cvd_score import calculate_cvd_risk

def test_comprehensive_risk_breakdown():
    """Test that comprehensive risk breakdown is correctly calculated and saved"""
    
    # Test user with mixed risk factors
    test_user = {
        'id': 'test_user',
        'age': 50,
        'gender': 'male',
        'profile': {
            'clinical': {
                'total_cholesterol': 220.0,
                'hdl_cholesterol': 40.0,
                'systolic_blood_pressure': 140.0,
                'hypertension_treatment': False,
                'diabetes': False
            },
            'lifestyle': {
                'smoking': False,
                'exercise_frequency': 1,      # Light exercise
                'sleep_hours': 6,             # Borderline sleep
                'stress_level': 7,            # Moderate-high stress
                'social_support': 4,          # Low-moderate social support
                'work_life_balance': 3        # Poor work-life balance
            },
            'family_history': {
                'heart_attack': True,         # Some family history
                'high_blood_pressure': False,
                'high_cholesterol': True,
                'diabetes': False,
                'stroke': False,
                'coronary_heart_disease': False,
                'hyperlipidemia': False
            }
        }
    }
    
    print("🧪 TESTING COMPREHENSIVE CVD RISK BREAKDOWN")
    print("=" * 60)
    
    result = calculate_cvd_risk(test_user)
    
    if result.get('success'):
        print(f"\n📊 FINAL COMPREHENSIVE RISK:")
        print(f"   Overall Risk: {result['percentage']:.2f}%")
        print(f"   Risk Level: {result['risk_level']}")
        print(f"   Methodology: {result['methodology']}")
        
        print(f"\n🔬 DETAILED BREAKDOWN:")
        framingham = result['framingham_risk']
        family_adj = result['family_adjusted_risk'] 
        lifestyle_adj = result['lifestyle_adjusted_risk']
        
        family_impact = (family_adj - framingham) * 100
        lifestyle_impact = (lifestyle_adj - family_adj) * 100
        total_adjustment = (lifestyle_adj - framingham) * 100
        
        print(f"   1️⃣ Framingham Base:       {framingham*100:.2f}%")
        print(f"   2️⃣ + Family History:      {family_adj*100:.2f}% ({family_impact:+.2f}%)")
        print(f"   3️⃣ + Lifestyle Factors:   {lifestyle_adj*100:.2f}% ({lifestyle_impact:+.2f}%)")
        print(f"   📈 Total Adjustment:      {total_adjustment:+.2f}%")
        
        print(f"\n🎯 FACTORS APPLIED:")
        print(f"   Family History:")
        family_history = test_user['profile']['family_history']
        for condition, has_it in family_history.items():
            if has_it:
                print(f"     ✅ {condition}")
        
        print(f"   Lifestyle Factors:")
        lifestyle = test_user['profile']['lifestyle']
        print(f"     🏃‍♂️ Exercise: {lifestyle['exercise_frequency']} times/week")
        print(f"     😴 Sleep: {lifestyle['sleep_hours']} hours/night")
        print(f"     😰 Stress: {lifestyle['stress_level']}/10")
        print(f"     👥 Social Support: {lifestyle['social_support']}/10")
        print(f"     ⚖️ Work-Life Balance: {lifestyle['work_life_balance']}/10")
        
        # Test what would be saved in the score_data
        score_data = result['score_data']
        factors = score_data['factors']
        
        print(f"\n💾 SAVED BREAKDOWN DATA:")
        print(f"   Framingham Risk: {factors.get('framingham_risk', 'N/A')}")
        print(f"   Family Adjusted: {factors.get('family_adjusted_risk', 'N/A')}")
        print(f"   Lifestyle Adjusted: {factors.get('lifestyle_adjusted_risk', 'N/A')}")
        
        print(f"\n✅ SUCCESS: Comprehensive risk calculation includes:")
        print(f"   ✓ Clinical parameters (Framingham)")
        print(f"   ✓ Family history adjustments")
        print(f"   ✓ Lifestyle factor adjustments")
        print(f"   ✓ Detailed breakdown for user visibility")
        
    else:
        print(f"❌ CALCULATION FAILED: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    test_comprehensive_risk_breakdown()
