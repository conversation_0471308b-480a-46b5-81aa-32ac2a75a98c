"""
Login Module for MIT CVD App
Handles user authentication with simple username/password storage
"""
import streamlit as st
import json
import os
import hashlib
from datetime import datetime
from typing import Optional, Dict, Any
import uuid
import sys

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config import USERS_DB_PATH, STREAKS_DB_PATH, DATA_DIR

def hash_password(password: str) -> str:
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def load_users() -> list:
    """Load users from JSON file"""
    if os.path.exists(USERS_DB_PATH):
        try:
            with open(USERS_DB_PATH, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            return []
    return []

def save_users(users: list) -> bool:
    """Save users to JSON file"""
    try:
        with open(USERS_DB_PATH, 'w', encoding='utf-8') as f:
            json.dump(users, f, indent=2, ensure_ascii=False)
        return True
    except (IOError, OSError):
        return False

def find_user_by_username(username: str, users: list) -> Optional[Dict[Any, Any]]:
    """Find user by username"""
    for user in users:
        if user.get('username') == username:
            return user
    return None

def find_user_by_email(email: str, users: list) -> Optional[Dict[Any, Any]]:
    """Find user by email"""
    for user in users:
        if user.get('email') == email:
            return user
    return None

def create_user(username: str, email: str, password: str, name: str, gender: str = 'male') -> Dict[Any, Any]:
    """Create a new user"""
    return {
        "id": str(uuid.uuid4()),
        "username": username,
        "email": email,
        "password": hash_password(password),
        "name": name,
        "gender": gender,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat(),
        "profile": {
            "lifestyle": {
                "smoking": False,
                "exercise_frequency": 0,
                "stress_level": 5,
                "sleep_hours": 8
            },
            "family_history": {
                "heart_attack": False,
                "high_blood_pressure": False
            }
        },
        "cvd_scores": []
    }

def authenticate_user(username: str, password: str) -> Optional[Dict[Any, Any]]:
    """Authenticate user with username and password"""
    users = load_users()
    user = find_user_by_username(username, users)
    
    if user and user.get('password') == hash_password(password):
        return user
    return None

def create_user_streak_entry(user_id: str):
    """Cria entrada inicial no sistema de streaks para novo usuário"""
    try:
        # Carregar streaks existentes ou criar lista vazia
        if os.path.exists(STREAKS_DB_PATH):
            with open(STREAKS_DB_PATH, 'r', encoding='utf-8') as f:
                streaks = json.load(f)
        else:
            streaks = []
        
        # Verificar se usuário já existe no sistema de streaks
        for streak in streaks:
            if streak.get('user_id') == user_id:
                return  # Usuário já existe no sistema de streaks
        
        # Criar nova entrada de streak para o usuário
        new_streak = {
            "user_id": user_id,
            "current_streak": 0,
            "longest_streak": 0,
            "total_checkins": 0,
            "last_checkin": None,
            "points": 0,
            "achievements": []
        }
        
        streaks.append(new_streak)
        
        # Criar diretório se não existir
        os.makedirs(os.path.dirname(STREAKS_DB_PATH), exist_ok=True)
        
        # Salvar arquivo atualizado
        with open(STREAKS_DB_PATH, 'w', encoding='utf-8') as f:
            json.dump(streaks, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Entrada de streak criada para usuário {user_id}")
            
    except Exception as e:
        print(f"❌ Erro ao criar entrada de streak para usuário {user_id}: {str(e)}")

def initialize_user_data_files(user_id: str):
    """Inicializa todos os arquivos de dados necessários para um novo usuário"""
    print(f"🔧 Inicializando dados para usuário {user_id}")
    
    # Criar entrada no sistema de streaks
    create_user_streak_entry(user_id)
    
    # Adicionar outras inicializações futuras aqui
    # Por exemplo: criar entrada em sessions.json se necessário
    
    print(f"✅ Inicialização completa para usuário {user_id}")

def check_user_in_streaks(user_id: str) -> bool:
    """Verifica se um usuário existe no sistema de streaks"""
    try:
        if not os.path.exists(STREAKS_DB_PATH):
            return False
            
        with open(STREAKS_DB_PATH, 'r', encoding='utf-8') as f:
            streaks = json.load(f)
        
        for streak in streaks:
            if streak.get('user_id') == user_id:
                return True
        
        return False
        
    except Exception:
        return False

def register_user(username: str, email: str, password: str, name: str, gender: str = 'male') -> tuple[bool, str]:
    """Register a new user"""
    users = load_users()
    
    # Check if username already exists
    if find_user_by_username(username, users):
        return False, "Username already exists"
    
    # Check if email already exists
    if find_user_by_email(email, users):
        return False, "Email is already in use"
    
    # Create new user
    new_user = create_user(username, email, password, name, gender)
    users.append(new_user)
    
    # Save users
    if save_users(users):
        # Initialize all data files for the new user
        initialize_user_data_files(new_user['id'])
        
        # Check if everything was created correctly
        if check_user_in_streaks(new_user['id']):
            return True, "User created successfully! Data initialized."
        else:
            return True, "User created successfully! (Warning: some auxiliary data may not have been initialized)"
    else:
        return False, "Error saving user"

def handle_login_form():
    """Handle login form submission"""
    with st.form("login_form"):
        username = st.text_input("Username")
        password = st.text_input("Password", type="password")
        login_submit = st.form_submit_button("🔑 Login", type="primary")
        
        if login_submit:
            if not username or not password:
                st.error("Please fill in all fields")
            else:
                user = authenticate_user(username, password)
                if user:
                    st.session_state.logged_in = True
                    st.session_state.user = user
                    st.success("Login successful! Redirecting...")
                    # Force immediate redirect to home page
                    st.session_state.current_page = "Home"
                    st.rerun()
                else:
                    st.error("Incorrect username or password")

def handle_register_form():
    """Handle registration form submission"""
    with st.form("register_form"):
        reg_name = st.text_input("Full name")
        reg_username = st.text_input("Username (for login)")
        reg_email = st.text_input("Email")
        
        # Gender selection
        reg_gender = st.selectbox(
            "Gender",
            options=["male", "female"],
            format_func=lambda x: "Male" if x == "male" else "Female",
            help="This is used for accurate cardiovascular risk calculation"
        )
        
        reg_password = st.text_input("Password", type="password")
        reg_password_confirm = st.text_input("Confirm password", type="password")
        register_submit = st.form_submit_button("📝 Create Account", type="primary")
        
        if register_submit:
            if not all([reg_name, reg_username, reg_email, reg_password, reg_password_confirm]):
                st.error("Please fill in all fields")
            elif reg_password != reg_password_confirm:
                st.error("Passwords do not match")
            elif len(reg_password) < 6:
                st.error("Password must be at least 6 characters")
            else:
                success, message = register_user(reg_username, reg_email, reg_password, reg_name, reg_gender)
                if success:
                    st.success(message)
                    st.info("Now you can log in with your credentials")
                else:
                    st.error(message)

def main():
    """Main login interface"""
    st.title("🔐 Login - aidctive")
    
    # Initialize session state
    if 'logged_in' not in st.session_state:
        st.session_state.logged_in = False
    if 'user' not in st.session_state:
        st.session_state.user = None
    
    # If user is logged in, this should not be reached due to front_end.py logic
    # But we'll add this as a fallback
    if st.session_state.logged_in:
        st.success("You are already logged in! Use the side menu to navigate.")
        return
    
    # Login/Register tabs
    tab1, tab2 = st.tabs(["Login", "Register"])
    
    with tab1:
        st.subheader("Log In")
        handle_login_form()
    
    with tab2:
        st.subheader("Create New Account")
        handle_register_form()

if __name__ == "__main__":
    main()
