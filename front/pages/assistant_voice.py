import streamlit as st
import os
import sys
import tempfile
from datetime import datetime
from gtts import gTTS

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.modules.audio import AudioProcessor
from backend.modules.completation_text import TextCompletation
from config import TEMP_DIR

def main():
    st.title("🗣️ CVD Voice Assistant")
    st.markdown("---")

    # Check if user is logged in
    if 'user' not in st.session_state or not st.session_state.user:
        st.error("❌ You need to be logged in to use the voice assistant.")
        st.info("🔑 Please log in first.")
        return

    # User context for personalized responses
    user_context = {
        "name": st.session_state.user.get("name", ""),
        "age": st.session_state.user.get("age", None),
        "profile": st.session_state.user.get("profile", {}),
        "user_id": st.session_state.user.get("id", "")
    }

    st.markdown("### 🎤 How to use the Voice Assistant:")
    st.info("""
    1. **🎙️ Record your audio** - Use the button below or upload a file
    2. **🔄 Processing** - Audio will be converted to text via Whisper
    3. **🤖 AI Responds** - Text will be sent to the AI specialized in CVD
    4. **🔊 Audio response** - Response will be converted to audio using Google TTS
    """)
    
    st.markdown("### 💡 Example questions:")
    with st.expander("📝 See some examples of what you can ask"):
        st.markdown("""
        **🍎 About Nutrition:**
        - "What foods are good for the heart?"
        - "Can I eat eggs if I have high cholesterol?"
        - "How many servings of fruits should I eat per day?"
        
        **🏃 About Exercise:**
        - "What type of exercise is best for cardiovascular health?"
        - "How many minutes of exercise do I need per day?"
        - "Can I exercise if I have high blood pressure?"
        
        **❤️ About Cardiovascular Health:**
        - "How can I reduce my risk of heart attack?"
        - "What are the signs of heart problems?"
        - "How to control blood pressure naturally?"
        """)
    
    st.markdown("---")

    # Initialize session state for voice assistant
    if 'voice_conversation' not in st.session_state:
        st.session_state.voice_conversation = []

    # Audio recording section using Streamlit native audio input
    st.markdown("### 🎙️ Direct Voice Recording")
    
    st.info("""
    🎤 **How to use:**
    1. Click the record button below
    2. Allow microphone access when prompted
    3. Speak your question clearly
    4. Click stop when finished
    5. Audio will be processed automatically
    """)
    
    # Native Streamlit audio input
    audio_value = st.audio_input("🎤 Click to record your cardiovascular health question", key="voice_recorder")
    
    if audio_value is not None:
        st.success("✅ Audio recorded successfully!")
        
        # Show the recorded audio for user to listen
        st.markdown("**🔊 Your recorded audio:**")
        st.audio(audio_value, format='audio/wav')
        
        # Process the recorded audio
        with st.spinner("🔄 Processing your message..."):
            process_audio_input(audio_value, user_context, "recording")

    st.markdown("---")
    
    # Alternative: File upload option for pre-recorded files
    st.markdown("### 📁 Or upload an audio file")
    uploaded_audio = st.file_uploader(
        "Upload an audio file",
        type=['mp3', 'wav', 'm4a', 'webm', 'ogg'],
        key="audio_upload"
    )
    
    if uploaded_audio is not None:
        with st.spinner("🔄 Processing uploaded audio..."):
            process_uploaded_audio(uploaded_audio, user_context)

    # Clear conversation button
    _, col2, _ = st.columns([1, 1, 1])
    with col2:
        if st.button("🗑️ Clear Conversation", key="clear_conversation", use_container_width=True):
            st.session_state.voice_conversation = []
            st.success("Conversation cleared!")
            st.rerun()

    # Display conversation history
    display_conversation_history()

def process_audio_input(audio_bytes, user_context, source_type):
    """Process audio input from st.audio_input"""
    try:
        # Save the audio bytes to a temporary file
        temp_path = save_audio_bytes_to_file(audio_bytes)
        
        if temp_path:
            process_audio_file(temp_path, user_context, source_type)
        else:
            st.error("❌ Error processing recorded audio")
            
    except Exception as e:
        st.error(f"❌ Error processing audio input: {str(e)}")

def save_audio_bytes_to_file(audio_bytes):
    """Save audio bytes to a temporary file"""
    try:
        # Create temp directory
        os.makedirs(TEMP_DIR, exist_ok=True)
        
        # Generate unique filename
        timestamp = int(datetime.now().timestamp() * 1000)
        temp_path = os.path.join(TEMP_DIR, f"recorded_audio_{timestamp}.wav")
        
        # Save audio bytes to file
        with open(temp_path, 'wb') as f:
            f.write(audio_bytes.read())
        
        return temp_path
        
    except Exception as e:
        st.error(f"Error saving audio: {str(e)}")
        return None

def process_uploaded_audio(uploaded_file, user_context):
    """Process uploaded audio file"""
    try:
        # Save uploaded file to temp location
        temp_path = save_uploaded_audio(uploaded_file)
        
        if temp_path:
            process_audio_file(temp_path, user_context, "upload")
        else:
            st.error("❌ Error processing uploaded file")
            
    except Exception as e:
        st.error(f"❌ Error processing audio: {str(e)}")

def save_uploaded_audio(uploaded_file):
    """Save uploaded audio file to temporary location"""
    try:
        # Create temp directory
        os.makedirs(TEMP_DIR, exist_ok=True)
        
        # Generate unique filename
        timestamp = int(datetime.now().timestamp() * 1000)
        file_extension = os.path.splitext(uploaded_file.name)[1]
        temp_path = os.path.join(TEMP_DIR, f"uploaded_audio_{timestamp}{file_extension}")
        
        # Save uploaded file
        with open(temp_path, 'wb') as f:
            f.write(uploaded_file.read())
        
        return temp_path
        
    except Exception as e:
        st.error(f"Error saving file: {str(e)}")
        return None

def process_audio_file(audio_path, user_context, source_type):
    """Complete audio processing pipeline: speech-to-text -> AI -> text-to-speech"""
    
    try:
        # Step 1: Speech-to-Text using Whisper
        st.info("🔄 **Step 1:** Converting audio to text...")
        
        audio_processor = AudioProcessor()
        transcription_result = audio_processor.transcribe_audio(audio_path, language="en")
        
        if not transcription_result.get('success', False):
            st.error(f"❌ Transcription error: {transcription_result.get('error', 'Unknown error')}")
            return
        
        user_text = transcription_result.get('transcript', '').strip()
        
        if not user_text:
            st.warning("⚠️ Could not detect text in audio. Try speaking more clearly.")
            return
        
        st.success(f"✅ **Text detected:** \"{user_text}\"")
        
        # Step 2: Generate AI response
        st.info("🔄 **Step 2:** Generating AI response...")
        
        ai_response = generate_ai_response(user_text, user_context)
        
        if not ai_response:
            st.error("❌ Error generating AI response")
            return
        
        st.success("✅ **AI response generated**")
        
        # Step 3: Text-to-Speech
        st.info("🔄 **Step 3:** Converting response to audio...")
        
        audio_response_path = text_to_speech(ai_response)
        
        if audio_response_path:
            st.success("✅ **Audio response created**")
            
            # Add to conversation history
            add_to_conversation(user_text, ai_response, audio_response_path, source_type)
            
            # Display the interaction
            display_current_interaction(user_text, ai_response, audio_response_path)
            
        else:
            st.error("❌ Error generating response audio")
            
    except Exception as e:
        st.error(f"❌ Processing error: {str(e)}")
    
    finally:
        # Clean up temporary audio file
        cleanup_temp_file(audio_path)

def generate_ai_response(user_text, user_context):
    """Generate AI response based on user input and context"""
    try:
        # Build context-aware prompt for CVD health assistant
        user_name = user_context.get('name', 'user')
        user_age = user_context.get('age', '')
        
        system_prompt = f"""
        You are a voice assistant specialized in cardiovascular health (CVD). 
        
        User context:
        - Name: {user_name}
        - Age: {user_age if user_age else 'not informed'}
        
        Instructions:
        1. Respond in a conversational and friendly manner
        2. Keep responses concise (maximum 3-4 sentences)
        3. Focus on cardiovascular health, nutrition and exercise
        4. Use simple and accessible language
        5. Include practical tips when relevant
        6. If the question is not about health, gently redirect to cardiovascular health topics
        
        User question: {user_text}
        
        Respond naturally and helpfully:
        """
        
        # Use TextCompletation for AI response
        text_completion = TextCompletation()
        ai_response = text_completion.completion(system_prompt)
        
        return ai_response.strip()
        
    except Exception as e:
        st.error(f"Error generating response: {str(e)}")
        return None

def text_to_speech(text):
    """Convert text to speech using gTTS"""
    try:
        # Create temp directory
        os.makedirs(TEMP_DIR, exist_ok=True)
        
        # Generate unique filename
        timestamp = int(datetime.now().timestamp() * 1000)
        audio_path = os.path.join(TEMP_DIR, f"tts_response_{timestamp}.mp3")
        
        # Generate speech
        tts = gTTS(text=text, lang='en', slow=False)
        tts.save(audio_path)
        
        return audio_path
        
    except Exception as e:
        st.error(f"Error in speech synthesis: {str(e)}")
        return None

def add_to_conversation(user_text, ai_response, audio_path, source_type):
    """Add interaction to conversation history"""
    conversation_entry = {
        'timestamp': datetime.now().strftime("%H:%M:%S"),
        'user_text': user_text,
        'ai_response': ai_response,
        'audio_path': audio_path,
        'source_type': source_type
    }
    
    st.session_state.voice_conversation.append(conversation_entry)

def display_current_interaction(user_text, ai_response, audio_path):
    """Display the current interaction"""
    st.markdown("### 💬 Current Interaction")
    
    # User input
    st.markdown("**👤 You said:**")
    st.info(f"🗣️ \"{user_text}\"")
    
    # AI response
    st.markdown("**🤖 CVD Assistant responded:**")
    st.success(ai_response)
    
    # Audio response
    if audio_path and os.path.exists(audio_path):
        st.markdown("**🔊 Audio response:**")
        
        # Read audio file and create download button
        with open(audio_path, 'rb') as audio_file:
            audio_bytes = audio_file.read()
        
        # Display audio player
        st.audio(audio_bytes, format='audio/mp3')
        
        # Download button
        st.download_button(
            label="⬇️ Download Audio Response",
            data=audio_bytes,
            file_name=f"assistant_response_{datetime.now().strftime('%H%M%S')}.mp3",
            mime="audio/mpeg"
        )

def display_conversation_history():
    """Display conversation history"""
    if not st.session_state.voice_conversation:
        return
    
    st.markdown("---")
    st.markdown("### 📝 Conversation History")
    
    # Show conversations in reverse order (most recent first)
    for i, entry in enumerate(reversed(st.session_state.voice_conversation)):
        with st.expander(f"💬 Conversation {len(st.session_state.voice_conversation) - i} - {entry['timestamp']}"):
            
            # User input
            st.markdown("**👤 You:**")
            st.write(f"🗣️ \"{entry['user_text']}\"")
            
            # AI response
            st.markdown("**🤖 Assistant:**")
            st.write(entry['ai_response'])
            
            # Audio player if available
            if entry['audio_path'] and os.path.exists(entry['audio_path']):
                try:
                    with open(entry['audio_path'], 'rb') as audio_file:
                        audio_bytes = audio_file.read()
                    st.audio(audio_bytes, format='audio/mp3')
                except Exception:
                    st.caption("🔇 Audio not available")
            
            st.caption(f"📥 Source: {entry['source_type']}")

def cleanup_temp_file(file_path):
    """Clean up temporary files"""
    try:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
    except Exception:
        pass  # Silent fail for cleanup

if __name__ == "__main__":
    main()
