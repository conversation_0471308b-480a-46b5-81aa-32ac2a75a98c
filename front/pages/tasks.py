import streamlit as st
import os
import sys
import tempfile
import json
from datetime import datetime, date

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.modules.completation_vision import VisionAnalyzer
from backend.modules.gamification import update_streak, log_activity
from config import TEMP_DIR

# Constants
BACK_BUTTON_TEXT = "🔙 Back"

def main():
    st.title("📋 Daily Tasks - aidctive")
    st.markdown("---")

    # Check if user is logged in
    if 'user' not in st.session_state or not st.session_state.user:
        st.error("❌ You need to be logged in to use this feature.")
        st.info("🔑 Please log in first.")
        return

    # Initialize states
    if 'daily_tasks' not in st.session_state:
        st.session_state.daily_tasks = {}
    if 'current_view' not in st.session_state:
        st.session_state.current_view = "main"  # main, meal, exercise
    if 'selected_meal' not in st.session_state:
        st.session_state.selected_meal = None

    # Get user context
    user_context = {
        "age": st.session_state.user.get("age", None),
        "name": st.session_state.user.get("name", ""),
        "profile": st.session_state.user.get("profile", {}),
        "user_id": st.session_state.user.get("id", "")
    }

    # View router
    if st.session_state.current_view == "main":
        show_main_view()
    elif st.session_state.current_view == "meal":
        show_meal_view(user_context)
    elif st.session_state.current_view == "exercise":
        show_exercise_view(user_context)

def show_main_view():
    """Displays the main task selection view"""
    show_daily_progress()
    
    st.markdown("---")
    st.subheader("🍽️ Meal Registration")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("☀️ Breakfast", use_container_width=True):
            st.session_state.selected_meal = "breakfast"
            st.session_state.current_view = "meal"
            st.rerun()
    
    with col2:
        if st.button("🌞 Lunch", use_container_width=True):
            st.session_state.selected_meal = "lunch"
            st.session_state.current_view = "meal"
            st.rerun()
    
    with col3:
        if st.button("🌅 Snack", use_container_width=True):
            st.session_state.selected_meal = "snack"
            st.session_state.current_view = "meal"
            st.rerun()
    
    with col4:
        if st.button("🌙 Dinner", use_container_width=True):
            st.session_state.selected_meal = "dinner"
            st.session_state.current_view = "meal"
            st.rerun()
            
    st.markdown("---")
    st.subheader("🏃‍♂️ Physical Activity")
    
    if st.button("📝 Log Physical Activity", use_container_width=True):
        st.session_state.current_view = "exercise"
        st.rerun()

def show_meal_view(user_context):
    """Displays the meal registration view"""
    meal_names = {
        "breakfast": "☀️ Breakfast",
        "lunch": "🌞 Lunch", 
        "snack": "🌅 Snack/Afternoon",
        "dinner": "🌙 Dinner"
    }
    
    selected_meal_name = meal_names[st.session_state.selected_meal]
    st.subheader(selected_meal_name)
    
    handle_meal_task(st.session_state.selected_meal, selected_meal_name, user_context)
    
    if st.button(BACK_BUTTON_TEXT, key="back_from_meal"):
        st.session_state.current_view = "main"
        st.session_state.selected_meal = None
        st.rerun()

def show_exercise_view(user_context):
    """Displays the exercise registration view"""
    st.subheader("🏃‍♂️ Physical Activity")
    handle_exercise_task(user_context)
    
    if st.button(BACK_BUTTON_TEXT, key="back_from_exercise"):
        st.session_state.current_view = "main"
        st.rerun()

def show_daily_progress():
    """Shows daily task progress"""
    
    # Initialize or get today's progress
    today = date.today().isoformat()
    
    if 'daily_tasks' not in st.session_state:
        st.session_state.daily_tasks = {}
    
    if today not in st.session_state.daily_tasks:
        st.session_state.daily_tasks[today] = {
            'breakfast': {'completed': False, 'time': None},
            'lunch': {'completed': False, 'time': None},
            'snack': {'completed': False, 'time': None},
            'dinner': {'completed': False, 'time': None},
            'exercise': {'completed': False, 'time': None}
        }
    
    today_tasks = st.session_state.daily_tasks[today]
    
    # Calculate completion stats
    total_tasks = len(today_tasks)
    completed_tasks = sum(1 for task in today_tasks.values() if task['completed'])
    completion_rate = (completed_tasks / total_tasks) * 100
    
    # Display progress metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "✅ Completed Tasks", 
            f"{completed_tasks}/{total_tasks}",
            delta=f"{completion_rate:.0f}% complete"
        )
    
    with col2:
        meal_completed = sum(1 for key, task in today_tasks.items() 
                           if key != 'exercise' and task['completed'])
        st.metric("🍽️ Meals", f"{meal_completed}/4")
    
    with col3:
        exercise_status = "✅ Done" if today_tasks['exercise']['completed'] else "❌ Pending"
        st.metric("🏃‍♂️ Exercise", exercise_status)
    
    with col4:
        # Show streak info
        try:
            from backend.modules.crud import load_data
            streaks_data = load_data('streaks.json')
            user_streak = streaks_data.get(st.session_state.user.get('id', ''), {})
            current_streak = user_streak.get('current_streak', 0)
            st.metric("🔥 Streak", f"{current_streak} days")
        except Exception:
            st.metric("🔥 Streak", "0 days")
    
    # Progress bar
    st.progress(completion_rate / 100)
    
    if completion_rate == 100:
        st.success("🎉 Congratulations! You completed all tasks for the day!")
    elif completion_rate >= 75:
        st.info("💪 Almost there! Just a few more tasks!")
    elif completion_rate >= 50:
        st.warning("📈 Good progress! Keep it up!")

def handle_meal_task(meal_type, meal_name, user_context):
    """Handles meal tasks with visual analysis"""
    
    today = date.today().isoformat()
    today_tasks = st.session_state.daily_tasks[today]
    meal_task = today_tasks[meal_type]
    
    # Check if already completed today
    if meal_task['completed']:
        st.success(f"✅ {meal_name} has already been logged today at {meal_task['time']}")
        
        if st.button(f"🔄 Log {meal_name} again", key=f"retry_{meal_type}"):
            meal_task['completed'] = False
            meal_task['time'] = None
        return
    
    st.write("📸 Take or upload a photo of your meal for nutritional analysis")
    
    # Initialize photo state for this meal
    photo_state_key = f"photo_state_{meal_type}"
    if photo_state_key not in st.session_state:
        st.session_state[photo_state_key] = "selection"  # selection, camera, upload
    
    photo_state = st.session_state[photo_state_key]
    
    if photo_state == "selection":
        # Show selection buttons
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📸 Take Photo", key=f"camera_btn_{meal_type}", use_container_width=True):
                st.session_state[photo_state_key] = "camera"
        
        with col2:
            if st.button("📁 Upload", key=f"upload_btn_{meal_type}", use_container_width=True):
                st.session_state[photo_state_key] = "upload"
    
    elif photo_state == "camera":
        st.markdown("**📸 Take a photo of your meal:**")
        
        camera_photo = st.camera_input("Take a photo of your physical activity", key="camera_tasks")
        
        if camera_photo is not None:
            st.success("📸 Photo captured! Analyzing...")
            process_meal_image(camera_photo, meal_type, meal_name, user_context)
        
        if st.button(BACK_BUTTON_TEXT, key=f"back_camera_{meal_type}"):
            st.session_state[photo_state_key] = "selection"
    
    elif photo_state == "upload":
        st.markdown("**📁 Upload a photo of your meal:**")
        
        upload_key = f"upload_{meal_type}_{today}"
        uploaded_file = st.file_uploader(
            "Choose an image",
            type=['jpg', 'jpeg', 'png', 'webp'],
            key=upload_key
        )
        
        if uploaded_file is not None:
            st.success("📁 File uploaded! Analyzing...")
            process_meal_image(uploaded_file, meal_type, meal_name, user_context)
        
        if st.button(BACK_BUTTON_TEXT, key=f"back_upload_{meal_type}"):
            st.session_state[photo_state_key] = "selection"

def handle_exercise_task(user_context):
    """Handles physical activity task with manual input"""
    
    today = date.today().isoformat()
    today_tasks = st.session_state.daily_tasks[today]
    exercise_task = today_tasks['exercise']
    
    # Check if already completed today
    if exercise_task['completed']:
        st.success(f"✅ Physical activity has already been logged today at {exercise_task['time']}")
        
        # Show activity details if available
        if 'analysis' in exercise_task and exercise_task['analysis']:
            analysis = exercise_task['analysis']
            if analysis.get('activity_type'):
                st.info(f"**Activity:** {analysis.get('activity_type')} | **Duration:** {analysis.get('duration', 'N/A')} min | **Intensity:** {analysis.get('intensity_level', 'N/A')}")
        
        if st.button("� Log activity again", key="retry_exercise"):
            exercise_task['completed'] = False
            exercise_task['time'] = None
            st.rerun()
        return
    
    st.write("📝 Log your physical activity manually")
    st.markdown("---")
    
    # Direct manual input form
    activity_type = st.selectbox(
        "🏃‍♂️ Type of activity:",
        ["Walking", "Running", "Cycling", "Swimming", "Weight Training", "Yoga", "Dancing", "Soccer", "Basketball", "Tennis", "Other"],
        help="Select the type of physical activity you performed"
    )
    
    col1, col2 = st.columns(2)
    with col1:
        duration = st.number_input(
            "⏱️ Duration (minutes):", 
            min_value=1, 
            max_value=300, 
            value=30,
            help="How long did you exercise?"
        )
    with col2:
        intensity = st.selectbox(
            "💪 Intensity:", 
            ["Light", "Moderate", "Intense"],
            help="How intense was your workout?"
        )
    
    # Optional notes
    notes = st.text_area(
        "📝 Additional notes (optional):",
        placeholder="Any additional details about your activity...",
        max_chars=200
    )
    
    if st.button("✅ Register Physical Activity", key="register_exercise", use_container_width=True):
        register_manual_exercise(activity_type, duration, intensity, user_context, notes)

def process_meal_image(image_file, meal_type, meal_name, user_context):
    """Processa imagem de refeição e realiza análise"""
    
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.image(image_file, caption=f"{meal_name}", use_container_width=True)
    
    with col2:
        temp_path = save_temp_image(image_file, f"meal_{meal_type}")
        
        if temp_path:
            with st.spinner(f"🔍 Analisando {meal_name.lower()}..."):
                analyzer = VisionAnalyzer()
                result = analyzer.analyze_food(temp_path, user_context)
                
                if result.get('success'):
                    display_meal_analysis(result)
                    
                    if st.button(f"✅ Confirmar {meal_name}", key=f"confirm_{meal_type}"):
                        complete_meal_task(meal_type, result, user_context)
                else:
                    st.error(f"❌ Erro na análise: {result.get('error', 'Erro desconhecido')}")
                
                cleanup_temp_file(temp_path)

def display_meal_analysis(result):
    """Exibe resultados da análise de refeição"""
    
    raw_response = result.get('raw_response', '')
    
    # Try to parse JSON if present
    try:
        if '{' in raw_response and '}' in raw_response:
            json_start = raw_response.find('{')
            json_end = raw_response.rfind('}') + 1
            json_str = raw_response[json_start:json_end]
            analysis_data = json.loads(json_str)
        else:
            analysis_data = {}
    except json.JSONDecodeError:
        analysis_data = {}
    
    st.markdown("### 🔍 Análise Nutricional")
    
    # Food identification
    food_name = analysis_data.get('food_name', 'Alimento identificado')
    st.markdown(f"**🍽️ Alimento:** {food_name}")
    
    # Heart health score
    health_score = analysis_data.get('heart_health_score', 5)
    if health_score >= 8:
        score_emoji = "💚"
    elif health_score >= 6:
        score_emoji = "💛"
    else:
        score_emoji = "❤️"
    
    st.markdown(f"**{score_emoji} Score Cardiovascular:** {health_score}/10")
    
    # Nutritional info
    nutrition = analysis_data.get('nutrition', {})
    if nutrition:
        with st.expander("📊 Informações Nutricionais"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.write(f"**Calorias:** {nutrition.get('calories', 'N/A')}")
                st.write(f"**Gordura Saturada:** {nutrition.get('saturated_fat', 'N/A')}g")
                st.write(f"**Sódio:** {nutrition.get('sodium', 'N/A')}mg")
            
            with col2:
                st.write(f"**Fibras:** {nutrition.get('fiber', 'N/A')}g")
                st.write(f"**Açúcar:** {nutrition.get('sugar', 'N/A')}g")
                st.write(f"**Proteína:** {nutrition.get('protein', 'N/A')}g")
    
    # CVD risk factors
    risk_factors = analysis_data.get('cvd_risk_factors', [])
    if risk_factors:
        st.markdown("**⚠️ Fatores de Risco Cardiovascular:**")
        for factor in risk_factors:
            st.write(f"• {factor}")
    
    # Recommendations
    recommendations = analysis_data.get('recommendations', [])
    if recommendations:
        with st.expander("💡 Recomendações"):
            for rec in recommendations:
                st.write(f"• {rec}")
    
    # Show raw analysis if no structured data
    if not analysis_data:
        with st.expander("📝 Análise Completa"):
            st.text(raw_response)

def complete_meal_task(meal_type, analysis_result, user_context):
    """Marca a tarefa de refeição como concluída"""
    
    today = date.today().isoformat()
    current_time = datetime.now().strftime("%H:%M")
    
    # Update task status
    st.session_state.daily_tasks[today][meal_type] = {
        'completed': True,
        'time': current_time,
        'analysis': analysis_result
    }
    
    # Update gamification
    try:
        user_id = user_context.get('user_id', '')
        update_streak(user_id, 'nutrition_log')
        log_activity(user_id, 'meal_photo')
        
        st.success(f"✅ {meal_type.title()} registrado com sucesso!")
        st.balloons()
        
        # Navigate back to main tasks page after successful meal registration
        st.session_state.current_view = "main"
        st.rerun()
        
    except Exception as e:
        st.warning(f"⚠️ Refeição registrada, mas houve um problema com a gamificação: {str(e)}")
        
        # Even if gamification fails, still navigate back to main page
        st.session_state.current_view = "main"
        st.rerun()

def register_manual_exercise(activity_type, duration, intensity, user_context, notes=None):
    """Registra exercício manualmente"""
    
    today = date.today().isoformat()
    current_time = datetime.now().strftime("%H:%M")
    
    # Create manual analysis result
    raw_response = f"Activity registered manually: {activity_type}, {duration} minutes, intensity {intensity.lower()}"
    if notes:
        raw_response += f" - Notes: {notes}"
    
    manual_result = {
        'success': True,
        'activity_type': activity_type,
        'duration': duration,
        'intensity_level': intensity.lower(),
        'notes': notes if notes else "",
        'source': 'manual_input',
        'raw_response': raw_response
    }
    
    # Update task status
    st.session_state.daily_tasks[today]['exercise'] = {
        'completed': True,
        'time': current_time,
        'analysis': manual_result
    }
    
    # Update gamification
    try:
        user_id = user_context.get('user_id', '')
        update_streak(user_id, 'exercise_report')
        log_activity(user_id, 'manual_exercise')
        
        st.success("✅ Physical activity registered successfully!")
        st.balloons()
        
        # Navigate back to main tasks page after successful registration
        st.session_state.current_view = "main"
        st.rerun()
        
    except Exception as e:
        st.warning(f"⚠️ Activity registered, but there was a problem with gamification: {str(e)}")
        
        # Even if gamification fails, still navigate back to main page
        st.session_state.current_view = "main"
        st.rerun()

def save_temp_image(image_file, prefix="task"):
    """Salva imagem temporária"""
    try:
        temp_dir = os.path.join(os.getcwd(), 'temp')
        os.makedirs(temp_dir, exist_ok=True)
        
        timestamp = int(datetime.now().timestamp() * 1000)
        temp_path = os.path.join(temp_dir, f"{prefix}_{timestamp}.jpg")
        
        with open(temp_path, "wb") as f:
            f.write(image_file.getvalue())
        
        return temp_path
    except Exception as e:
        st.error(f"Erro ao salvar imagem: {str(e)}")
        return None

def cleanup_temp_file(file_path):
    """Remove arquivo temporário"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception:
        pass  # Ignore cleanup errors

if __name__ == "__main__":
    main()
