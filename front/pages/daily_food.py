import streamlit as st
import os
import sys
import tempfile
from PIL import Image
import json

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.modules.completation_vision import VisionAnalyzer
from config import TEMP_DIR, DATA_DIR, FOOD_HISTORY_PATH, USERS_DB_PATH

def main():
    st.title("📸 Food Nutritional Analysis")
    st.markdown("---")

    # Check if user is logged in
    if 'user' not in st.session_state or not st.session_state.user:
        st.error("❌ You need to be logged in to use this feature.")
        st.info("🔑 Please log in first.")
        return

    # Get user context for personalized analysis
    user_context = {
        "age": st.session_state.user.get("age", None),
        "name": st.session_state.user.get("name", ""),
        "profile": st.session_state.user.get("profile", {}),
        "user_id": st.session_state.user.get("id", "")
    }

    # Image input options
    st.subheader("📷 Capture or Upload a Food Photo")
    
    # Tabs for different input methods
    tab1, tab2 = st.tabs(["📸 Take Photo", "📁 File Upload"])
    
    with tab1:
        st.markdown("### Take a photo of your food")
        camera_photo = st.camera_input("Click to take a photo", key="camera_photo_daily")
        
        if camera_photo is not None:
            process_image(camera_photo, user_context, "camera")
    
    with tab2:
        st.markdown("### Upload a photo from your file")
        uploaded_file = st.file_uploader(
            "Choose an image",
            type=['jpg', 'jpeg', 'png', 'webp', 'gif'],
            help="Supported formats: JPG, JPEG, PNG, WEBP, GIF"
        )
        
        if uploaded_file is not None:
            process_image(uploaded_file, user_context, "upload")
    
    # Show history sidebar
    show_history_sidebar()

def process_image(image_file, user_context, source_type):
    """Processes the image and performs nutritional analysis"""
    
    # Show the image
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.image(image_file, caption=f"Image captured via {source_type}", use_container_width=True)
    
    with col2:
        # Save image temporarily
        temp_path = save_temp_image(image_file)
        
        if temp_path:
            with st.spinner("🔍 Analyzing the food..."):
                # Perform analysis
                analyzer = VisionAnalyzer()
                result = analyzer.analyze_food(temp_path, user_context)
                
                # Show results
                display_analysis_results(result)
                
                # Clean up temp file
                cleanup_temp_file(temp_path)
        else:
            st.error("❌ Error processing image. Please try again.")

def save_temp_image(image_file):
    """Saves the image to a temporary file"""
    try:
        # Create temp directory if it doesn't exist
        os.makedirs(TEMP_DIR, exist_ok=True)
        
        # Generate unique filename
        import time
        timestamp = int(time.time() * 1000)
        temp_path = os.path.join(TEMP_DIR, f"food_analysis_{timestamp}.jpg")
        
        # Save the image
        if hasattr(image_file, 'read'):
            # It's a file sent via streamlit
            image_data = image_file.read()
            with open(temp_path, 'wb') as f:
                f.write(image_data)
        else:
            # It's a PIL image
            image_file.save(temp_path)
        
        return temp_path
    except Exception as e:
        st.error(f"Error saving image: {str(e)}")
        return None

def display_analysis_results(result):
    """Displays the results of nutritional analysis"""
    
    # Check for error field in result
    if 'error' in result:
        error_message = result.get('error', 'Unknown error')
        
        # Check for specific "no food" error patterns
        no_food_patterns = [
            'no food',
            'not a food',
            'appears to be a selfie',
            'not food item',
            'no food image detected',
            'food not detected',
            'not food-related',
            'does not contain food'
        ]
        
        # Check if error message contains any "no food" patterns
        error_lower = error_message.lower()
        is_no_food_error = any(pattern in error_lower for pattern in no_food_patterns)
        
        if is_no_food_error:
            st.warning("🚫 No Food Detected")
            st.info("📷 **Please upload a clear image of food for analysis.**")
            st.markdown(f"💡 **Tip:** {error_message}")
            
            # Show helpful suggestions
            with st.expander("📋 Tips for better food analysis"):
                st.markdown("""
                - **Take a clear photo** of the food from above
                - **Ensure good lighting** for better detection
                - **Show the entire dish** or meal
                - **Avoid selfies or non-food images**
                - **Include multiple food items** if it's a complete meal
                """)
        else:
            st.error("❌ Analysis error:")
            st.error(error_message)
        
        return
    
    # Check legacy success field
    if not result.get('success', False):
        st.error("❌ Analysis error:")
        st.error(result.get('error', 'Unknown error'))
        return
    
    st.success("✅ Analysis completed successfully!")
    
    # Food name and score
    display_food_info(result)
    
    # Nutritional information
    display_nutrition_info(result.get('nutrition', {}))
    
    # Risk factors and recommendations
    display_risks_and_recommendations(result)
    
    # Action buttons
    display_action_buttons(result)

def display_food_info(result):
    """Displays basic food information"""
    food_name = result.get('food_name', 'Food not identified')
    st.subheader(f"🍽️ {food_name}")
    
    health_score = result.get('heart_health_score', 0)
    
    # Convert to float if it's a string
    try:
        health_score = float(health_score)
    except (ValueError, TypeError):
        health_score = 5.0  # Default value if conversion fails
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric(
            label="💚 Heart Health Score",
            value=f"{health_score}/10",
            help="Score from 1-10, where 10 is most heart-healthy"
        )
        st.progress(health_score / 10)
    
    with col2:
        # Show potential CVD score impact
        show_cvd_impact_preview(result)

def show_cvd_impact_preview(result):
    """Shows preview of potential CVD score impact"""
    try:
        health_score = result.get('heart_health_score', 5)
        
        # Convert to float if it's a string
        try:
            health_score = float(health_score)
        except (ValueError, TypeError):
            health_score = 5.0  # Default value if conversion fails
            
        cvd_risk_factors = result.get('cvd_risk_factors', [])
        
        # Simple impact estimation
        if health_score >= 8:
            impact_text = "📈 Positive Impact"
            impact_color = "green"
            impact_desc = "This food may improve your CVD score"
        elif health_score >= 6:
            impact_text = "⚖️ Neutral Impact"
            impact_color = "orange"
            impact_desc = "This food has moderate impact on CVD"
        else:
            impact_text = "📉 Negative Impact"
            impact_color = "red"
            impact_desc = "This food may worsen your CVD score"
        
        st.markdown("**🎯 Impact on CVD Score:**")
        st.markdown(f"<span style='color: {impact_color}'><b>{impact_text}</b></span>", unsafe_allow_html=True)
        st.caption(impact_desc)
        
        if cvd_risk_factors:
            st.warning(f"⚠️ Risk factors identified: {len(cvd_risk_factors)}")
            
    except Exception:
        st.caption("Error calculating CVD impact")

def display_nutrition_info(nutrition):
    """Displays nutritional information"""
    if not nutrition:
        return
        
    st.subheader("📊 Nutritional Information (per serving)")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("🔥 Calories", f"{nutrition.get('calories', 'N/A')}")
        st.metric("🧈 Saturated Fat", f"{nutrition.get('saturated_fat', 'N/A')} g")
    
    with col2:
        st.metric("🧂 Sodium", f"{nutrition.get('sodium', 'N/A')} mg")
        st.metric("🌾 Fiber", f"{nutrition.get('fiber', 'N/A')} g")
    
    with col3:
        st.metric("🍯 Sugar", f"{nutrition.get('sugar', 'N/A')} g")
        st.metric("🥩 Protein", f"{nutrition.get('protein', 'N/A')} g")

def display_risks_and_recommendations(result):
    """Displays risk factors and recommendations"""
    # Cardiovascular risk factors
    cvd_risks = result.get('cvd_risk_factors', [])
    if cvd_risks:
        st.subheader("⚠️ Cardiovascular Risk Factors")
        for risk in cvd_risks:
            st.warning(f"• {risk}")
    
    # Recommendations
    recommendations = result.get('recommendations', [])
    if recommendations:
        st.subheader("💡 Recommendations")
        for rec in recommendations:
            st.info(f"• {rec}")

def display_action_buttons(result):
    """Displays action buttons"""
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("💾 Save Analysis", key="save_analysis_btn"):
            save_analysis_to_history(result)
            update_cvd_score_with_nutrition(result)
            st.success("✅ Analysis saved and CVD Score updated!")
            st.rerun()
    
    with col2:
        if st.button("🔄 Recalculate CVD Score", key="recalc_cvd_btn"):
            update_cvd_score_with_nutrition(result)
            st.success("🎯 CVD Score recalculated based on nutritional analysis!")
            st.rerun()
    
    with col3:
        # Show organized detailed analysis
        with st.expander("🔍 View Detailed Analysis"):
            display_detailed_analysis(result)

def display_detailed_analysis(result):
    """Displays detailed analysis in an organized way"""
    try:
        # Section 1: Basic information
        st.markdown("### 📋 **Analysis Summary**")
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**🍽️ Food:** {result.get('food_name', 'N/A')}")
            health_score = result.get('heart_health_score', 0)
            try:
                health_score = float(health_score)
            except (ValueError, TypeError):
                health_score = 0.0
            st.write(f"**💚 Heart Score:** {health_score}/10")
        
        with col2:
            timestamp = result.get('analysis_timestamp', 'N/A')
            st.write(f"**📅 Analyzed on:** {timestamp[:19] if timestamp != 'N/A' else 'N/A'}")
            cvd_factors = result.get('cvd_risk_factors', [])
            st.write(f"**⚠️ Risk Factors:** {len(cvd_factors)}")
        
        # Section 2: Detailed nutritional information
        nutrition = result.get('nutrition', {})
        if nutrition:
            st.markdown("### 📊 **Nutritional Details**")
            
            # Create an organized table
            nutrition_data = []
            for key, value in nutrition.items():
                # Translate keys to English
                key_translations = {
                    'calories': 'Calories',
                    'saturated_fat': 'Saturated Fat (g)',
                    'trans_fat': 'Trans Fat (g)',
                    'sodium': 'Sodium (mg)',
                    'fiber': 'Fiber (g)',
                    'sugar': 'Sugar (g)',
                    'protein': 'Protein (g)',
                    'carbohydrates': 'Carbohydrates (g)',
                    'total_fat': 'Total Fat (g)'
                }
                
                translated_key = key_translations.get(key, key.title())
                nutrition_data.append([translated_key, str(value)])
            
            if nutrition_data:
                import pandas as pd
                df = pd.DataFrame(nutrition_data, columns=['Nutrient', 'Value'])
                st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Section 3: Risk factors
        if cvd_factors:
            st.markdown("### ⚠️ **CVD Risk Factors Identified**")
            for factor in cvd_factors:
                st.warning(f"• {factor}")
        
        # Section 4: Recommendations
        recommendations = result.get('recommendations', [])
        if recommendations:
            st.markdown("### 💡 **Personalized Recommendations**")
            for rec in recommendations:
                st.info(f"• {rec}")
        
        # Section 5: Raw response (for debug)
        st.markdown("### 🔧 **Technical Data**")
        raw_response = result.get('raw_response', 'No data available')
        
        with st.expander("View raw AI response"):
            if isinstance(raw_response, dict):
                st.json(raw_response)
            elif isinstance(raw_response, str):
                try:
                    import json
                    parsed_json = json.loads(raw_response)
                    st.json(parsed_json)
                except json.JSONDecodeError:
                    st.text(raw_response)
            else:
                st.write(raw_response)
                
    except Exception as e:
        st.error(f"Error displaying detailed analysis: {str(e)}")
        # Fallback for basic display
        st.json(result)

def save_analysis_to_history(result):
    """Saves the analysis to user history"""
    try:
        # Create data directory if it doesn't exist
        os.makedirs(DATA_DIR, exist_ok=True)
        
        # Load existing history or create new
        if os.path.exists(FOOD_HISTORY_PATH):
            with open(FOOD_HISTORY_PATH, 'r', encoding='utf-8') as f:
                history = json.load(f)
        else:
            history = []
        
        # Add new analysis
        health_score = result.get('heart_health_score', 5)
        try:
            health_score = float(health_score)
        except (ValueError, TypeError):
            health_score = 5.0
            
        analysis_entry = {
            'timestamp': result.get('analysis_timestamp'),
            'food_name': result.get('food_name'),
            'heart_health_score': health_score,
            'nutrition': result.get('nutrition'),
            'cvd_risk_factors': result.get('cvd_risk_factors'),
            'recommendations': result.get('recommendations')
        }
        
        history.append(analysis_entry)
        
        # Save updated history
        with open(FOOD_HISTORY_PATH, 'w', encoding='utf-8') as f:
            json.dump(history, f, indent=2, ensure_ascii=False)
            
    except Exception as e:
        st.error(f"Error saving to history: {str(e)}")

def update_cvd_score_with_nutrition(result):
    """Updates the user's CVD score based on nutritional analysis"""
    try:
        from backend.modules.cvd_score import CVDRiskCalculator
        
        # Get current user data
        user_data = st.session_state.user.copy()
        
        # Load existing nutritional logs
        nutrition_logs = user_data.get('nutrition_logs', [])
        
        # Add new analysis to nutritional logs
        health_score = result.get('heart_health_score', 5)
        try:
            health_score = float(health_score)
        except (ValueError, TypeError):
            health_score = 5.0
            
        new_nutrition_log = {
            'timestamp': result.get('analysis_timestamp'),
            'food_name': result.get('food_name'),
            'nutrition': result.get('nutrition'),
            'heart_health_score': health_score,
            'cvd_risk_factors': result.get('cvd_risk_factors')
        }
        
        nutrition_logs.append(new_nutrition_log)
        user_data['nutrition_logs'] = nutrition_logs
        
        # Recalculate CVD score with new nutritional data
        calculator = CVDRiskCalculator()
        new_cvd_result = calculator.calculate_cvd_risk(user_data)
        
        if new_cvd_result.get('success'):
            # Add new CVD score to user history
            cvd_scores = user_data.get('cvd_scores', [])
            cvd_scores.append(new_cvd_result['score_data'])
            user_data['cvd_scores'] = cvd_scores
            
            # Save updated user data to file
            save_user_data_to_file(user_data)
            
            # Update session state
            st.session_state.user = user_data
            
            st.info(f"🎯 **New CVD Score:** {new_cvd_result['score_data']['score']:.1%} ({new_cvd_result['score_data']['risk_level']})")
        else:
            st.error("Error calculating new CVD score")
            
    except Exception as e:
        st.error(f"Error updating CVD score: {str(e)}")
        import traceback
        st.error(traceback.format_exc())

def save_user_data_to_file(user_data):
    """Saves updated user data to users.json file"""
    try:
        # Load existing users
        if os.path.exists(USERS_DB_PATH):
            with open(USERS_DB_PATH, 'r', encoding='utf-8') as f:
                users = json.load(f)
        else:
            users = []
        
        # Find and update the user
        user_id = user_data.get('id')
        for i, user in enumerate(users):
            if user.get('id') == user_id:
                users[i] = user_data
                break
        else:
            # If not found, add as new user
            users.append(user_data)
        
        # Save updated data
        with open(USERS_DB_PATH, 'w', encoding='utf-8') as f:
            json.dump(users, f, indent=2, ensure_ascii=False)
            
    except Exception as e:
        st.error(f"Error saving user data: {str(e)}")

def cleanup_temp_file(file_path):
    """Removes temporary file"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception:
        pass  # Silent failure on cleanup

# Sidebar with history
def get_score_color(score):
    """Get color emoji for score"""
    if score >= 8:
        return "🟢"
    elif score >= 6:
        return "🟡" 
    else:
        return "🔴"

def load_user_history(user_id):
    """Load user's food analysis history"""
    try:
        history_file = os.path.join(DATA_DIR, f'food_analysis_history_{user_id}.json')
        if os.path.exists(history_file):
            with open(history_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception:
        pass
    return []

def show_history_sidebar():
    """Shows analysis history in sidebar"""
    with st.sidebar:
        st.markdown("---")
        st.subheader("📋 Recent History")
        
        # Check if user is logged in
        if 'user' not in st.session_state or not st.session_state.user:
            st.info("Log in to view your history")
            return
        
        user_id = st.session_state.user.get('id', '')
        history = load_user_history(user_id)
        
        if history:
            # Show last 5 analyses
            recent_history = history[-5:]
            
            for entry in reversed(recent_history):
                food_name = entry.get('food_name', 'Unknown food')
                score = entry.get('heart_health_score', 0)
                timestamp = entry.get('timestamp', '')
                
                score_color = get_score_color(score)
                
                st.markdown(f"**{food_name}**")
                st.markdown(f"{score_color} Score: {score}/10")
                if timestamp:
                    st.markdown(f"_{timestamp[:10]}_")
                st.markdown("---")
        else:
            st.info("No analyses yet")

if __name__ == "__main__":
    # Initialize session state for testing
    if 'user' not in st.session_state:
        st.session_state.user = {
            "name": "Test User",
            "age": 30,
            "id": "test_user_id",
            "profile": {}
        }
    main()
    show_history_sidebar()
