#!/usr/bin/env python3
"""
Test script to verify lifestyle adjustments are working in CVD risk calculation
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(__file__))

from backend.modules.cvd_score import calculate_cvd_risk

def test_lifestyle_impact():
    """Test that lifestyle factors actually change the CVD risk score"""
    
    # Base user data with clinical parameters
    base_user = {
        'id': 'test_user',
        'age': 45,
        'gender': 'male',
        'profile': {
            'clinical': {
                'total_cholesterol': 200.0,
                'hdl_cholesterol': 45.0,
                'systolic_blood_pressure': 130.0,
                'hypertension_treatment': False,
                'diabetes': False
            },
            'lifestyle': {
                'smoking': False,
                'exercise_frequency': 0,  # Sedentary
                'sleep_hours': 8,
                'stress_level': 5,
                'social_support': 5,
                'work_life_balance': 5
            },
            'family_history': {}
        }
    }
    
    print("🧪 Testing CVD Risk Calculation with Lifestyle Adjustments")
    print("=" * 60)
    
    # Test 1: Sedentary lifestyle
    result1 = calculate_cvd_risk(base_user)
    print(f"\n1️⃣ SEDENTARY LIFESTYLE:")
    print(f"   Exercise: {base_user['profile']['lifestyle']['exercise_frequency']} times/week")
    print(f"   CVD Risk: {result1['percentage']:.2f}%")
    print(f"   Risk Level: {result1['risk_level']}")
    
    # Test 2: Very active lifestyle
    active_user = base_user.copy()
    active_user['profile'] = base_user['profile'].copy()
    active_user['profile']['lifestyle'] = base_user['profile']['lifestyle'].copy()
    active_user['profile']['lifestyle']['exercise_frequency'] = 6  # Very active
    
    result2 = calculate_cvd_risk(active_user)
    print(f"\n2️⃣ VERY ACTIVE LIFESTYLE:")
    print(f"   Exercise: {active_user['profile']['lifestyle']['exercise_frequency']} times/week")
    print(f"   CVD Risk: {result2['percentage']:.2f}%")
    print(f"   Risk Level: {result2['risk_level']}")
    
    # Test 3: High stress
    stressed_user = base_user.copy()
    stressed_user['profile'] = base_user['profile'].copy()
    stressed_user['profile']['lifestyle'] = base_user['profile']['lifestyle'].copy()
    stressed_user['profile']['lifestyle']['stress_level'] = 9  # High stress
    
    result3 = calculate_cvd_risk(stressed_user)
    print(f"\n3️⃣ HIGH STRESS:")
    print(f"   Stress Level: {stressed_user['profile']['lifestyle']['stress_level']}/10")
    print(f"   CVD Risk: {result3['percentage']:.2f}%")
    print(f"   Risk Level: {result3['risk_level']}")
    
    # Test 4: Poor sleep
    poor_sleep_user = base_user.copy()
    poor_sleep_user['profile'] = base_user['profile'].copy()
    poor_sleep_user['profile']['lifestyle'] = base_user['profile']['lifestyle'].copy()
    poor_sleep_user['profile']['lifestyle']['sleep_hours'] = 4  # Poor sleep
    
    result4 = calculate_cvd_risk(poor_sleep_user)
    print(f"\n4️⃣ POOR SLEEP:")
    print(f"   Sleep Hours: {poor_sleep_user['profile']['lifestyle']['sleep_hours']} hours/night")
    print(f"   CVD Risk: {result4['percentage']:.2f}%")
    print(f"   Risk Level: {result4['risk_level']}")
    
    # Test 5: Optimal lifestyle
    optimal_user = base_user.copy()
    optimal_user['profile'] = base_user['profile'].copy()
    optimal_user['profile']['lifestyle'] = {
        'smoking': False,
        'exercise_frequency': 5,  # Very active
        'sleep_hours': 7.5,       # Optimal sleep
        'stress_level': 2,        # Low stress
        'social_support': 9,      # Strong social support
        'work_life_balance': 8    # Good work-life balance
    }
    
    result5 = calculate_cvd_risk(optimal_user)
    print(f"\n5️⃣ OPTIMAL LIFESTYLE:")
    print(f"   Exercise: {optimal_user['profile']['lifestyle']['exercise_frequency']} times/week")
    print(f"   Sleep: {optimal_user['profile']['lifestyle']['sleep_hours']} hours/night")
    print(f"   Stress: {optimal_user['profile']['lifestyle']['stress_level']}/10")
    print(f"   Social Support: {optimal_user['profile']['lifestyle']['social_support']}/10")
    print(f"   CVD Risk: {result5['percentage']:.2f}%")
    print(f"   Risk Level: {result5['risk_level']}")
    
    print("\n" + "=" * 60)
    print("📊 COMPARISON SUMMARY:")
    print(f"   Sedentary:     {result1['percentage']:.2f}%")
    print(f"   Very Active:   {result2['percentage']:.2f}%")
    print(f"   High Stress:   {result3['percentage']:.2f}%")
    print(f"   Poor Sleep:    {result4['percentage']:.2f}%")
    print(f"   Optimal:       {result5['percentage']:.2f}%")
    
    # Calculate differences
    exercise_impact = result1['percentage'] - result2['percentage']
    stress_impact = result3['percentage'] - result1['percentage']
    sleep_impact = result4['percentage'] - result1['percentage']
    optimal_impact = result1['percentage'] - result5['percentage']
    
    print(f"\n💡 LIFESTYLE IMPACT:")
    print(f"   Exercise benefit:      -{exercise_impact:.2f}% risk reduction")
    print(f"   High stress penalty:   +{stress_impact:.2f}% risk increase")
    print(f"   Poor sleep penalty:    +{sleep_impact:.2f}% risk increase")
    print(f"   Optimal lifestyle:     -{optimal_impact:.2f}% total risk reduction")

if __name__ == "__main__":
    test_lifestyle_impact()
