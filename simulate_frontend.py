#!/usr/bin/env python3
"""
Simulate frontend CVD risk display
"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from backend.modules.crud import list_users

def simulate_frontend_display():
    """Simulate how the frontend would display CVD risk"""
    print("🖥️ SIMULATING FRONTEND CVD RISK DISPLAY")
    print("=" * 60)
    
    users = list_users()
    if not users:
        print("❌ No users found")
        return
    
    user = users[0]
    print(f"👤 User: {user.get('name', 'Unknown')}")
    
    # Simulate the frontend logic
    cvd_scores = user.get('cvd_scores', [])
    if cvd_scores:
        latest_score = cvd_scores[-1]
        score_value = latest_score['score']
        risk_level = latest_score['risk_level']
        
        print(f"\n📈 **Comprehensive CVD Risk**")
        print(f"   Risk Level: {risk_level}")
        print(f"   10-Year Risk: {score_value:.1%}")
        
        # Show breakdown if available
        if hasattr(latest_score, 'get') and latest_score.get('factors'):
            factors = latest_score['factors']
            
            print(f"\n📊 **Risk Score Breakdown**:")
            
            # Check for new format first (Framingham-based)
            if 'framingham_risk' in factors:
                framingham_pct = factors['framingham_risk'] * 100
                print(f"   🔬 **Framingham Base**: {framingham_pct:.1f}%")
                
                if 'family_adjusted_risk' in factors:
                    family_adj = factors['family_adjusted_risk'] * 100
                    family_impact = family_adj - factors['framingham_risk'] * 100
                    print(f"   🧬 **After Family History**: {family_adj:.1f}% ({family_impact:+.1f}%)")
                
                if 'lifestyle_adjusted_risk' in factors:
                    lifestyle_adj = factors['lifestyle_adjusted_risk'] * 100
                    if 'family_adjusted_risk' in factors:
                        lifestyle_impact = lifestyle_adj - factors['family_adjusted_risk'] * 100
                    else:
                        lifestyle_impact = lifestyle_adj - factors.get('framingham_risk', 0) * 100
                    print(f"   🏃‍♂️ **After Lifestyle**: {lifestyle_adj:.1f}% ({lifestyle_impact:+.1f}%)")
            
            # Fallback to old format if new format not available
            elif 'age_risk' in factors:
                print(f"   ⚠️ **Legacy Format** - Recalculate for detailed breakdown")
                if 'age_risk' in factors:
                    print(f"   🎂 **Age Factor**: {factors.get('age_risk', 0):.1f}%")
                if 'family_risk' in factors:
                    print(f"   🧬 **Family Factor**: {factors.get('family_risk', 0):.1f}%")
                if 'lifestyle_risk' in factors:
                    print(f"   🏃‍♂️ **Lifestyle Factor**: {factors.get('lifestyle_risk', 0):.1f}%")
            
            else:
                print(f"   ℹ️ Detailed breakdown not available - recalculate risk score")
        
        # Show last update time
        last_update = latest_score.get('calculated_at', '')
        if last_update:
            print(f"\n   Updated: {last_update[:10]}")
        
        # Show methodology
        print(f"   Includes Framingham + Lifestyle + Family History")
        
        # Check if we have previous scores to compare
        if len(cvd_scores) > 1:
            previous_score = cvd_scores[-2]
            prev_value = previous_score.get('score', 0)
            change = score_value - prev_value
            change_pct = change * 100
            
            print(f"\n📈 **Trend Analysis**:")
            if change > 0.01:  # >1% increase
                print(f"   📈 Increased by {change_pct:+.1f}%")
            elif change < -0.01:  # >1% decrease
                print(f"   📉 Decreased by {change_pct:+.1f}%")
            else:
                print(f"   ➡️ Stable ({change_pct:+.1f}%)")
    else:
        print(f"\n📈 **Comprehensive CVD Risk**")
        print(f"   ℹ️ No assessment yet")
        print(f"   Complete clinical profile to calculate")

if __name__ == "__main__":
    simulate_frontend_display()
