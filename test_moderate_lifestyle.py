#!/usr/bin/env python3
"""
Test lifestyle adjustments with moderate risk baseline
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(__file__))

from backend.modules.cvd_score import calculate_cvd_risk

def test_moderate_baseline():
    """Test lifestyle adjustments starting from moderate baseline"""
    
    # Moderate baseline user
    moderate_user = {
        'id': 'test_user',
        'age': 45,
        'gender': 'male',
        'profile': {
            'clinical': {
                'total_cholesterol': 200.0,
                'hdl_cholesterol': 45.0,
                'systolic_blood_pressure': 130.0,
                'hypertension_treatment': False,
                'diabetes': False
            },
            'lifestyle': {
                'smoking': False,
                'exercise_frequency': 3,
                'sleep_hours': 7,
                'stress_level': 5,
                'social_support': 5,
                'work_life_balance': 5
            },
            'family_history': {
                'heart_attack': False,
                'high_blood_pressure': False,
                'high_cholesterol': False,
                'diabetes': False,
                'stroke': False,
                'coronary_heart_disease': False,
                'hyperlipidemia': False
            }
        }
    }
    
    print("🧪 TESTING LIFESTYLE ADJUSTMENTS FROM MODERATE BASELINE")
    print("=" * 60)
    
    # Test 1: Baseline
    baseline_result = calculate_cvd_risk(moderate_user)
    print(f"\n1️⃣ BASELINE (Moderate profile):")
    print(f"   CVD Risk: {baseline_result['percentage']:.2f}%")
    print(f"   Exercise: {moderate_user['profile']['lifestyle']['exercise_frequency']} times/week")
    print(f"   Sleep: {moderate_user['profile']['lifestyle']['sleep_hours']} hours")
    print(f"   Stress: {moderate_user['profile']['lifestyle']['stress_level']}/10")
    
    # Test 2: Sedentary + poor lifestyle
    bad_lifestyle_user = moderate_user.copy()
    bad_lifestyle_user['profile'] = moderate_user['profile'].copy()
    bad_lifestyle_user['profile']['lifestyle'] = {
        'smoking': False,
        'exercise_frequency': 0,      # Sedentary
        'sleep_hours': 4,             # Poor sleep
        'stress_level': 9,            # High stress
        'social_support': 2,          # Poor social support
        'work_life_balance': 2        # Poor work-life balance
    }
    
    bad_result = calculate_cvd_risk(bad_lifestyle_user)
    print(f"\n2️⃣ BAD LIFESTYLE:")
    print(f"   CVD Risk: {bad_result['percentage']:.2f}%")
    print(f"   Exercise: {bad_lifestyle_user['profile']['lifestyle']['exercise_frequency']} times/week")
    print(f"   Sleep: {bad_lifestyle_user['profile']['lifestyle']['sleep_hours']} hours")
    print(f"   Stress: {bad_lifestyle_user['profile']['lifestyle']['stress_level']}/10")
    
    # Test 3: Excellent lifestyle
    good_lifestyle_user = moderate_user.copy()
    good_lifestyle_user['profile'] = moderate_user['profile'].copy()
    good_lifestyle_user['profile']['lifestyle'] = {
        'smoking': False,
        'exercise_frequency': 6,      # Very active
        'sleep_hours': 7.5,           # Optimal sleep
        'stress_level': 2,            # Low stress
        'social_support': 9,          # Strong social support
        'work_life_balance': 9        # Excellent work-life balance
    }
    
    good_result = calculate_cvd_risk(good_lifestyle_user)
    print(f"\n3️⃣ EXCELLENT LIFESTYLE:")
    print(f"   CVD Risk: {good_result['percentage']:.2f}%")
    print(f"   Exercise: {good_lifestyle_user['profile']['lifestyle']['exercise_frequency']} times/week")
    print(f"   Sleep: {good_lifestyle_user['profile']['lifestyle']['sleep_hours']} hours")
    print(f"   Stress: {good_lifestyle_user['profile']['lifestyle']['stress_level']}/10")
    
    # Calculate differences
    bad_vs_baseline = bad_result['percentage'] - baseline_result['percentage']
    good_vs_baseline = baseline_result['percentage'] - good_result['percentage']
    total_range = bad_result['percentage'] - good_result['percentage']
    
    print(f"\n📊 LIFESTYLE IMPACT COMPARISON:")
    print(f"   Baseline:         {baseline_result['percentage']:.2f}%")
    print(f"   Bad Lifestyle:    {bad_result['percentage']:.2f}% ({bad_vs_baseline:+.2f}%)")
    print(f"   Good Lifestyle:   {good_result['percentage']:.2f}% ({-good_vs_baseline:+.2f}%)")
    print(f"   Total Range:      {total_range:.2f}% difference")
    
    if total_range > 1.0:
        print(f"   ✅ Lifestyle adjustments are working properly!")
    else:
        print(f"   ⚠️  Lifestyle adjustments may be too small")
    
    # Show detailed breakdown
    print(f"\n🔬 DETAILED BREAKDOWN (Bad Lifestyle):")
    if 'family_adjusted_risk' in bad_result:
        framingham = bad_result['framingham_risk']
        family_adj = bad_result['family_adjusted_risk'] 
        lifestyle_adj = bad_result['lifestyle_adjusted_risk']
        
        family_impact = (family_adj - framingham) * 100
        lifestyle_impact = (lifestyle_adj - family_adj) * 100
        
        print(f"   Framingham:       {framingham*100:.2f}%")
        print(f"   Family adjusted:  {family_adj*100:.2f}% ({family_impact:+.2f}%)")
        print(f"   Lifestyle adj:    {lifestyle_adj*100:.2f}% ({lifestyle_impact:+.2f}%)")

if __name__ == "__main__":
    test_moderate_baseline()
