#!/usr/bin/env python3
"""
Debug script to check the exact structure of saved CVD score data
"""

import json
from backend.modules.cvd_score import FraminghamRiskCalculator
from backend.modules.crud import list_users

def debug_score_structure():
    """Debug the structure of CVD score data"""
    print("🔍 DEBUGGING CVD SCORE DATA STRUCTURE")
    print("=" * 60)
    
    # Test data
    test_user = {
        'id': 'test_user',
        'age': 50,
        'gender': 'male',
        'profile': {
            'clinical': {
                'total_cholesterol': 220,
                'hdl_cholesterol': 40,
                'systolic_blood_pressure': 140,
                'hypertension_treatment': True,
                'diabetes': False
            },
            'family_history': {
                'heart_attack': True,
                'high_cholesterol': True,
                'stroke': False,
                'diabetes': False,
                'hypertension': False
            },
            'lifestyle': {
                'exercise_frequency': 1,
                'sleep_hours': 6,
                'stress_level': 7,
                'social_support': 4,
                'work_life_balance': 3
            }
        }
    }
    
    # Calculate CVD risk
    calculator = FraminghamRiskCalculator()
    result = calculator.calculate_cvd_risk(test_user)
    
    if result.get('success'):
        print("✅ CVD calculation successful")
        print("\n📊 RESULT STRUCTURE:")
        print(f"   Keys in result: {list(result.keys())}")
        
        print(f"\n   Score: {result.get('score')}")
        print(f"   Risk Level: {result.get('risk_level')}")
        print(f"   Percentage: {result.get('percentage')}")
        
        # Check individual risk components
        print(f"\n🔬 RISK COMPONENTS:")
        print(f"   Framingham Risk: {result.get('framingham_risk')}")
        print(f"   Family Adjusted Risk: {result.get('family_adjusted_risk')}")
        print(f"   Lifestyle Adjusted Risk: {result.get('lifestyle_adjusted_risk')}")
        
        # Check score_data structure
        if 'score_data' in result:
            score_data = result['score_data']
            print(f"\n📦 SCORE_DATA STRUCTURE:")
            print(f"   Keys in score_data: {list(score_data.keys())}")
            
            if 'factors' in score_data:
                factors = score_data['factors']
                print(f"\n🧬 FACTORS STRUCTURE:")
                print(f"   Keys in factors: {list(factors.keys())}")
                print(f"   Framingham Risk in factors: {factors.get('framingham_risk')}")
                print(f"   Family Adjusted Risk in factors: {factors.get('family_adjusted_risk')}")
                print(f"   Lifestyle Adjusted Risk in factors: {factors.get('lifestyle_adjusted_risk')}")
            else:
                print("\n❌ No 'factors' found in score_data")
        else:
            print("\n❌ No 'score_data' found in result")
    else:
        print(f"❌ CVD calculation failed: {result.get('error')}")
    
    # Also check what's actually saved in users.json
    print(f"\n📁 CHECKING ACTUAL USER DATA:")
    users = list_users()
    if users:
        for user in users:
            cvd_scores = user.get('cvd_scores', [])
            if cvd_scores:
                latest = cvd_scores[-1]
                print(f"   User: {user.get('name', 'Unknown')}")
                print(f"   Latest score keys: {list(latest.keys())}")
                
                if 'factors' in latest:
                    factors = latest['factors']
                    print(f"   Factors keys: {list(factors.keys())}")
                    print(f"   Framingham: {factors.get('framingham_risk')}")
                    print(f"   Family: {factors.get('family_adjusted_risk')}")
                    print(f"   Lifestyle: {factors.get('lifestyle_adjusted_risk')}")
                else:
                    print(f"   ❌ No 'factors' in latest score")
                    # Check if they're at top level
                    print(f"   Framingham at top: {latest.get('framingham_risk')}")
                    print(f"   Family at top: {latest.get('family_adjusted_risk')}")
                    print(f"   Lifestyle at top: {latest.get('lifestyle_adjusted_risk')}")
                break
    else:
        print("   No users found")

if __name__ == "__main__":
    debug_score_structure()
