#!/usr/bin/env python3
"""
Debug script to check why family history and lifestyle aren't affecting risk score
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(__file__))

from backend.modules.cvd_score import calculate_cvd_risk

def debug_risk_calculation():
    """Debug risk calculation step by step"""
    
    # Base user with worst possible profile
    worst_case_user = {
        'id': 'test_user',
        'age': 60,
        'gender': 'male',
        'profile': {
            'clinical': {
                'total_cholesterol': 250.0,  # High
                'hdl_cholesterol': 35.0,     # Low (bad)
                'systolic_blood_pressure': 160.0,  # High
                'hypertension_treatment': True,
                'diabetes': True
            },
            'lifestyle': {
                'smoking': True,              # Worst
                'exercise_frequency': 0,      # Sedentary - worst
                'sleep_hours': 4,             # Very poor sleep - worst
                'stress_level': 10,           # Maximum stress - worst
                'social_support': 1,          # No social support - worst
                'work_life_balance': 1,       # Terrible work-life balance - worst
                'alcohol_consumption': 'Heavy',
                'diet_type': 'Standard'
            },
            'family_history': {
                'heart_attack': True,         # Worst
                'high_blood_pressure': True,  # Worst
                'high_cholesterol': True,     # Worst
                'diabetes': True,             # Worst
                'stroke': True,               # Worst
                'coronary_heart_disease': True,  # Worst
                'hyperlipidemia': True        # Worst
            }
        }
    }
    
    print("🚨 DEBUGGING CVD RISK CALCULATION - WORST CASE SCENARIO")
    print("=" * 80)
    
    # Calculate risk with detailed debugging
    result = calculate_cvd_risk(worst_case_user)
    
    if result.get('success'):
        print(f"\n📊 FINAL RESULTS:")
        print(f"   Final CVD Risk: {result['percentage']:.2f}%")
        print(f"   Risk Level: {result['risk_level']}")
        
        print(f"\n🔬 BREAKDOWN:")
        print(f"   Framingham Base Risk: {result.get('framingham_risk', 0):.4f} ({result.get('framingham_risk', 0)*100:.2f}%)")
        
        if 'family_adjusted_risk' in result:
            family_adj = result['family_adjusted_risk']
            framingham = result['framingham_risk']
            family_impact = (family_adj - framingham) * 100
            print(f"   After Family History: {family_adj:.4f} ({family_adj*100:.2f}%) [Impact: {family_impact:+.2f}%]")
        
        if 'lifestyle_adjusted_risk' in result:
            lifestyle_adj = result['lifestyle_adjusted_risk']
            family_adj = result.get('family_adjusted_risk', result['framingham_risk'])
            lifestyle_impact = (lifestyle_adj - family_adj) * 100
            print(f"   After Lifestyle Adj:  {lifestyle_adj:.4f} ({lifestyle_adj*100:.2f}%) [Impact: {lifestyle_impact:+.2f}%]")
        
        print(f"\n🎯 RISK FACTORS IDENTIFIED:")
        for factor in result.get('risk_factors', []):
            print(f"   ❌ {factor}")
        
        print(f"\n💚 PROTECTIVE FACTORS:")
        for factor in result.get('protective_factors', []):
            print(f"   ✅ {factor}")
        
        print(f"\n🧬 FAMILY HISTORY DATA:")
        family_history = worst_case_user['profile']['family_history']
        for condition, value in family_history.items():
            status = "YES" if value else "NO"
            print(f"   {condition}: {status}")
        
        print(f"\n🏃‍♂️ LIFESTYLE DATA:")
        lifestyle = worst_case_user['profile']['lifestyle']
        for factor, value in lifestyle.items():
            print(f"   {factor}: {value}")
            
    else:
        print(f"❌ CALCULATION FAILED: {result.get('error', 'Unknown error')}")
    
    print(f"\n" + "=" * 80)
    
    # Now test with optimal profile for comparison
    optimal_user = {
        'id': 'test_user_optimal',
        'age': 35,
        'gender': 'female',
        'profile': {
            'clinical': {
                'total_cholesterol': 180.0,  # Optimal
                'hdl_cholesterol': 70.0,     # High (good)
                'systolic_blood_pressure': 110.0,  # Optimal
                'hypertension_treatment': False,
                'diabetes': False
            },
            'lifestyle': {
                'smoking': False,             # Best
                'exercise_frequency': 6,      # Very active - best
                'sleep_hours': 7.5,           # Optimal sleep - best
                'stress_level': 2,            # Low stress - best
                'social_support': 9,          # Strong social support - best
                'work_life_balance': 9,       # Excellent work-life balance - best
                'alcohol_consumption': 'None',
                'diet_type': 'Mediterranean'
            },
            'family_history': {
                'heart_attack': False,        # Best
                'high_blood_pressure': False, # Best
                'high_cholesterol': False,    # Best
                'diabetes': False,            # Best
                'stroke': False,              # Best
                'coronary_heart_disease': False,  # Best
                'hyperlipidemia': False       # Best
            }
        }
    }
    
    print("💚 OPTIMAL CASE COMPARISON:")
    optimal_result = calculate_cvd_risk(optimal_user)
    
    if optimal_result.get('success'):
        print(f"   Optimal CVD Risk: {optimal_result['percentage']:.2f}%")
        print(f"   Risk Level: {optimal_result['risk_level']}")
        
        difference = result['percentage'] - optimal_result['percentage']
        print(f"   DIFFERENCE: {difference:.2f}% higher risk for worst case")
        
        if difference < 5:
            print("   ⚠️  WARNING: Difference is too small! Adjustments may not be working properly.")
        else:
            print("   ✅ Good: Significant difference between worst and optimal cases.")

if __name__ == "__main__":
    debug_risk_calculation()
