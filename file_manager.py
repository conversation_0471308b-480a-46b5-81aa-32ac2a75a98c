"""
File Management Module for MIT CVD App
Handles file uploads, temporary storage, cleanup, and validation
"""
import os
import shutil
import tempfile
import mimetypes
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging

# Import config for centralized paths
from config import TEMP_DIR

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileManager:
    """Comprehensive file management for uploaded files"""
    
    def __init__(self, temp_dir: str = None):
        self.temp_dir = temp_dir or TEMP_DIR
        self.max_file_size = 25 * 1024 * 1024  # 25MB
        self.allowed_audio_types = [
            'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/m4a', 
            'audio/mp4', 'audio/webm', 'audio/ogg'
        ]
        self.allowed_image_types = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 
            'image/gif', 'image/bmp'
        ]
        
        # Ensure temp directory exists
        os.makedirs(self.temp_dir, exist_ok=True)
    
    def validate_file(self, file_data: bytes, filename: str, file_type: str) -> Dict[str, Any]:
        """
        Validate uploaded file
        
        Args:
            file_data: Raw file bytes
            filename: Original filename
            file_type: Expected file type ('audio' or 'image')
            
        Returns:
            Dict with validation results
        """
        try:
            # Check file size
            if len(file_data) > self.max_file_size:
                return {
                    'valid': False,
                    'error': f'File too large: {len(file_data)} bytes (max: {self.max_file_size})'
                }
            
            if len(file_data) == 0:
                return {
                    'valid': False,
                    'error': 'Empty file'
                }
            
            # Check filename
            if not filename or len(filename) > 255:
                return {
                    'valid': False,
                    'error': 'Invalid filename'
                }
            
            # Guess MIME type
            mime_type, _ = mimetypes.guess_type(filename)
            
            # Validate file type
            if file_type == 'audio':
                allowed_types = self.allowed_audio_types
            elif file_type == 'image':
                allowed_types = self.allowed_image_types
            else:
                return {
                    'valid': False,
                    'error': f'Unsupported file type category: {file_type}'
                }
            
            if mime_type not in allowed_types:
                return {
                    'valid': False,
                    'error': f'Unsupported MIME type: {mime_type}. Allowed: {allowed_types}'
                }
            
            return {
                'valid': True,
                'mime_type': mime_type,
                'file_size': len(file_data)
            }
            
        except Exception as e:
            logger.error(f"File validation error: {e}")
            return {
                'valid': False,
                'error': f'Validation failed: {str(e)}'
            }
    
    def save_file(self, file_data: bytes, filename: str, file_type: str) -> Dict[str, Any]:
        """
        Save uploaded file to temporary storage
        
        Args:
            file_data: Raw file bytes
            filename: Original filename
            file_type: File type ('audio' or 'image')
            
        Returns:
            Dict with save results including temp path
        """
        try:
            # Validate file first
            validation = self.validate_file(file_data, filename, file_type)
            if not validation['valid']:
                return {
                    'success': False,
                    'error': validation['error']
                }
            
            # Generate safe filename
            safe_filename = self._generate_safe_filename(filename, file_type)
            temp_path = os.path.join(self.temp_dir, safe_filename)
            
            # Save file
            with open(temp_path, 'wb') as f:
                f.write(file_data)
            
            logger.info(f"Saved {file_type} file: {temp_path}")
            
            return {
                'success': True,
                'temp_path': temp_path,
                'original_filename': filename,
                'safe_filename': safe_filename,
                'file_size': len(file_data),
                'mime_type': validation['mime_type']
            }
            
        except Exception as e:
            logger.error(f"Error saving file: {e}")
            return {
                'success': False,
                'error': f'Failed to save file: {str(e)}'
            }
    
    def cleanup_file(self, file_path: str) -> bool:
        """
        Clean up temporary file
        
        Args:
            file_path: Path to file to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if os.path.exists(file_path) and self.temp_dir in file_path:
                os.remove(file_path)
                logger.debug(f"Cleaned up file: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error cleaning up file {file_path}: {e}")
            return False
    
    def cleanup_old_files(self, max_age_hours: int = 24) -> Dict[str, Any]:
        """
        Clean up old temporary files
        
        Args:
            max_age_hours: Maximum age of files to keep in hours
            
        Returns:
            Dict with cleanup results
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            cleaned_files = []
            total_size_freed = 0
            
            for filename in os.listdir(self.temp_dir):
                file_path = os.path.join(self.temp_dir, filename)
                
                if os.path.isfile(file_path):
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    if file_mtime < cutoff_time:
                        file_size = os.path.getsize(file_path)
                        if self.cleanup_file(file_path):
                            cleaned_files.append(filename)
                            total_size_freed += file_size
            
            logger.info(f"Cleaned up {len(cleaned_files)} old files, freed {total_size_freed} bytes")
            
            return {
                'success': True,
                'files_cleaned': len(cleaned_files),
                'size_freed': total_size_freed,
                'cleaned_files': cleaned_files
            }
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_temp_dir_stats(self) -> Dict[str, Any]:
        """Get statistics about temporary directory"""
        try:
            total_files = 0
            total_size = 0
            file_types = {}
            
            for filename in os.listdir(self.temp_dir):
                file_path = os.path.join(self.temp_dir, filename)
                
                if os.path.isfile(file_path):
                    total_files += 1
                    file_size = os.path.getsize(file_path)
                    total_size += file_size
                    
                    # Count by type
                    if filename.startswith('audio_'):
                        file_types['audio'] = file_types.get('audio', 0) + 1
                    elif filename.startswith('image_'):
                        file_types['image'] = file_types.get('image', 0) + 1
                    else:
                        file_types['other'] = file_types.get('other', 0) + 1
            
            return {
                'temp_dir': self.temp_dir,
                'total_files': total_files,
                'total_size': total_size,
                'file_types': file_types,
                'disk_usage_mb': total_size / (1024 * 1024)
            }
            
        except Exception as e:
            logger.error(f"Error getting temp dir stats: {e}")
            return {
                'error': str(e)
            }
    
    def _generate_safe_filename(self, original_filename: str, file_type: str) -> str:
        """Generate safe filename for temporary storage"""
        # Extract extension
        _, ext = os.path.splitext(original_filename)
        if not ext:
            ext = '.tmp'
        
        # Clean original filename
        safe_base = "".join(c for c in original_filename if c.isalnum() or c in '._-')
        safe_base = safe_base[:50]  # Limit length
        
        # Generate timestamp-based filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
        return f"{file_type}_{timestamp}_{safe_base}{ext}"

# Global file manager instance
file_manager = FileManager()

# Convenience functions
def save_uploaded_audio(audio_data: bytes, filename: str) -> str:
    """Save uploaded audio file and return temp path"""
    result = file_manager.save_file(audio_data, filename, 'audio')
    if result['success']:
        return result['temp_path']
    else:
        raise Exception(result['error'])

def save_uploaded_image(image_data: bytes, filename: str) -> str:
    """Save uploaded image file and return temp path"""
    result = file_manager.save_file(image_data, filename, 'image')
    if result['success']:
        return result['temp_path']
    else:
        raise Exception(result['error'])

def cleanup_temp_file(file_path: str) -> bool:
    """Clean up temporary file"""
    return file_manager.cleanup_file(file_path)

def cleanup_old_temp_files(max_age_hours: int = 24) -> Dict[str, Any]:
    """Clean up old temporary files"""
    return file_manager.cleanup_old_files(max_age_hours)

def get_file_manager_stats() -> Dict[str, Any]:
    """Get file manager statistics"""
    return file_manager.get_temp_dir_stats()
