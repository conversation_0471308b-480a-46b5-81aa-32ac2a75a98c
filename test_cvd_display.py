#!/usr/bin/env python3
"""
Test script to verify the CVD risk display with proper breakdown
"""

import streamlit as st
import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from backend.modules.cvd_score import FraminghamRiskCalculator
from backend.modules.crud import list_users, update_user

def test_cvd_display():
    """Test CVD risk calculation and display"""
    print("🧪 TESTING CVD RISK DISPLAY WITH BREAKDOWN")
    print("=" * 60)
    
    # Get first user
    users = list_users()
    if not users:
        print("❌ No users found")
        return
    
    user = users[0]
    print(f"👤 Testing with user: {user.get('name', 'Unknown')}")
    
    # Check current CVD scores
    current_scores = user.get('cvd_scores', [])
    print(f"📊 Current scores count: {len(current_scores)}")
    
    if current_scores:
        latest = current_scores[-1]
        print(f"🔍 Latest score structure:")
        print(f"   Score: {latest.get('score')}")
        print(f"   Risk Level: {latest.get('risk_level')}")
        
        if 'factors' in latest:
            factors = latest['factors']
            print(f"   Factors available: {list(factors.keys())}")
            
            # Check format
            if 'framingham_risk' in factors:
                print("✅ New Framingham format detected")
                print(f"   Framingham: {factors['framingham_risk']:.4f}")
                print(f"   Family Adjusted: {factors.get('family_adjusted_risk', 'N/A')}")
                print(f"   Lifestyle Adjusted: {factors.get('lifestyle_adjusted_risk', 'N/A')}")
            elif 'age_risk' in factors:
                print("⚠️ Legacy format detected")
                print(f"   Age Risk: {factors.get('age_risk', 'N/A')}")
                print(f"   Family Risk: {factors.get('family_risk', 'N/A')}")
                print(f"   Lifestyle Risk: {factors.get('lifestyle_risk', 'N/A')}")
    
    # Force recalculation with new format
    print(f"\n🔄 RECALCULATING WITH NEW FORMAT:")
    calculator = FraminghamRiskCalculator()
    
    # Make sure user has required clinical data
    clinical = user.get('profile', {}).get('clinical', {})
    print(f"📋 Clinical data available:")
    print(f"   Total Cholesterol: {clinical.get('total_cholesterol')}")
    print(f"   HDL Cholesterol: {clinical.get('hdl_cholesterol')}")
    print(f"   Systolic BP: {clinical.get('systolic_blood_pressure')}")
    print(f"   BP Medication: {clinical.get('hypertension_treatment')}")
    print(f"   Diabetes: {clinical.get('diabetes')}")
    
    if all(clinical.get(field) is not None for field in ['total_cholesterol', 'hdl_cholesterol', 'systolic_blood_pressure']):
        result = calculator.calculate_cvd_risk(user)
        
        if result.get('success'):
            print(f"\n✅ NEW CALCULATION SUCCESSFUL:")
            print(f"   Score: {result['score']:.4f} ({result['percentage']:.1f}%)")
            print(f"   Risk Level: {result['risk_level']}")
            
            # Check the new score_data structure
            score_data = result['score_data']
            factors = score_data['factors']
            
            print(f"\n📊 NEW BREAKDOWN:")
            print(f"   Framingham Base: {factors['framingham_risk']:.4f} ({factors['framingham_risk']*100:.1f}%)")
            print(f"   Family Adjusted: {factors['family_adjusted_risk']:.4f} ({factors['family_adjusted_risk']*100:.1f}%)")
            print(f"   Lifestyle Adjusted: {factors['lifestyle_adjusted_risk']:.4f} ({factors['lifestyle_adjusted_risk']*100:.1f}%)")
            
            # Calculate impacts
            family_impact = (factors['family_adjusted_risk'] - factors['framingham_risk']) * 100
            lifestyle_impact = (factors['lifestyle_adjusted_risk'] - factors['family_adjusted_risk']) * 100
            
            print(f"\n📈 IMPACT ANALYSIS:")
            print(f"   Family History Impact: {family_impact:+.1f}%")
            print(f"   Lifestyle Impact: {lifestyle_impact:+.1f}%")
            print(f"   Total Adjustment: {(factors['lifestyle_adjusted_risk'] - factors['framingham_risk'])*100:+.1f}%")
            
            # Add this new score to user
            current_scores.append(score_data)
            
            # Update user
            update_success = update_user(user['id'], {'cvd_scores': current_scores})
            if update_success:
                print(f"\n✅ User updated with new score format")
            else:
                print(f"\n❌ Failed to update user")
        else:
            print(f"❌ Calculation failed: {result.get('error')}")
    else:
        print(f"❌ Missing required clinical data for calculation")

if __name__ == "__main__":
    test_cvd_display()
