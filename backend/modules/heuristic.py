"""
Custom Heuristics Module for MIT CVD App
Advanced heuristic calculations combining family history, habits, and nutrition data
"""
import math
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import statistics

from backend.models.data_models import current_timestamp

class CVDHeuristicEngine:
    """Advanced heuristic engine for CVD risk assessment"""
    
    def __init__(self):
        # Heuristic weights for different factors
        self.heuristic_weights = {
            'genetic_predisposition': 0.25,
            'lifestyle_consistency': 0.20,
            'nutrition_quality': 0.20,
            'behavioral_patterns': 0.15,
            'environmental_factors': 0.10,
            'temporal_trends': 0.10
        }
        
        # Lifestyle pattern scoring
        self.lifestyle_patterns = {
            'exercise_consistency': {'weight': 0.3, 'ideal_frequency': 4},
            'sleep_regularity': {'weight': 0.2, 'ideal_hours': 8},
            'stress_management': {'weight': 0.25, 'max_stress': 10},
            'social_support': {'weight': 0.15, 'baseline': 5},
            'work_life_balance': {'weight': 0.1, 'baseline': 5}
        }
    
    def calculate_heuristic(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate comprehensive heuristic score combining multiple factors
        
        Args:
            user_data: Complete user profile with history and patterns
            
        Returns:
            Dict with heuristic score, component analysis, and insights
        """
        try:
            # Extract data components
            profile = user_data.get('profile', {})
            cvd_scores = user_data.get('cvd_scores', [])
            nutrition_logs = user_data.get('nutrition_logs', [])
            chat_sessions = user_data.get('chat_sessions', [])
            
            # Calculate individual heuristic components
            genetic_score = self._calculate_genetic_predisposition(profile.get('family_history', {}))
            lifestyle_score = self._calculate_lifestyle_consistency(profile.get('lifestyle', {}), cvd_scores)
            nutrition_score = self._calculate_nutrition_quality(nutrition_logs)
            behavioral_score = self._calculate_behavioral_patterns(chat_sessions, user_data)
            environmental_score = self._calculate_environmental_factors(user_data)
            temporal_score = self._calculate_temporal_trends(cvd_scores, nutrition_logs)
            
            # Combine scores using weighted formula
            total_heuristic = (
                genetic_score * self.heuristic_weights['genetic_predisposition'] +
                lifestyle_score * self.heuristic_weights['lifestyle_consistency'] +
                nutrition_score * self.heuristic_weights['nutrition_quality'] +
                behavioral_score * self.heuristic_weights['behavioral_patterns'] +
                environmental_score * self.heuristic_weights['environmental_factors'] +
                temporal_score * self.heuristic_weights['temporal_trends']
            )
            
            # Generate insights and recommendations
            insights = self._generate_heuristic_insights({
                'genetic': genetic_score,
                'lifestyle': lifestyle_score,
                'nutrition': nutrition_score,
                'behavioral': behavioral_score,
                'environmental': environmental_score,
                'temporal': temporal_score
            }, user_data)
            
            return {
                'success': True,
                'heuristic_score': round(total_heuristic, 3),
                'risk_category': self._categorize_heuristic_risk(total_heuristic),
                'component_scores': {
                    'genetic_predisposition': round(genetic_score, 3),
                    'lifestyle_consistency': round(lifestyle_score, 3),
                    'nutrition_quality': round(nutrition_score, 3),
                    'behavioral_patterns': round(behavioral_score, 3),
                    'environmental_factors': round(environmental_score, 3),
                    'temporal_trends': round(temporal_score, 3)
                },
                'insights': insights,
                'calculation_timestamp': current_timestamp()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Heuristic calculation error: {str(e)}",
                'heuristic_score': 0.5
            }
    
    def _calculate_genetic_predisposition(self, family_history: Dict[str, Any]) -> float:
        """Calculate genetic predisposition score based on family history"""
        if not family_history:
            return 0.3  # Baseline unknown risk
        
        # Weighted genetic risk factors
        genetic_risks = {
            'heart_attack': {'weight': 0.4, 'early_onset_multiplier': 1.5},
            'stroke': {'weight': 0.3, 'early_onset_multiplier': 1.3},
            'high_blood_pressure': {'weight': 0.2, 'early_onset_multiplier': 1.2},
            'diabetes': {'weight': 0.25, 'early_onset_multiplier': 1.4},
            'high_cholesterol': {'weight': 0.15, 'early_onset_multiplier': 1.1}
        }
        
        total_genetic_risk = 0.0
        
        for condition, config in genetic_risks.items():
            if family_history.get(condition, False):
                base_risk = config['weight']
                
                # Check for early onset (increases risk)
                early_onset = family_history.get(f'{condition}_early_onset', False)
                if early_onset:
                    base_risk *= config['early_onset_multiplier']
                
                # Check for multiple family members
                multiple_affected = family_history.get(f'{condition}_multiple', False)
                if multiple_affected:
                    base_risk *= 1.3
                
                total_genetic_risk += base_risk
        
        return min(1.0, total_genetic_risk)
    
    def _calculate_lifestyle_consistency(self, lifestyle: Dict[str, Any], cvd_scores: List[Dict[str, Any]]) -> float:
        """Calculate lifestyle consistency and adherence patterns"""
        if not lifestyle:
            return 0.5  # Default moderate score
        
        consistency_score = 0.0
        
        # Exercise consistency
        exercise_freq = lifestyle.get('exercise_frequency', 0)
        ideal_freq = self.lifestyle_patterns['exercise_consistency']['ideal_frequency']
        exercise_score = min(1.0, exercise_freq / ideal_freq)
        consistency_score += exercise_score * self.lifestyle_patterns['exercise_consistency']['weight']
        
        # Sleep regularity
        sleep_hours = lifestyle.get('sleep_hours', 8)
        ideal_sleep = self.lifestyle_patterns['sleep_regularity']['ideal_hours']
        sleep_deviation = abs(sleep_hours - ideal_sleep)
        sleep_score = max(0.0, 1.0 - (sleep_deviation / 4))  # Penalty for deviation
        consistency_score += sleep_score * self.lifestyle_patterns['sleep_regularity']['weight']
        
        # Stress management
        stress_level = lifestyle.get('stress_level', 5)
        max_stress = self.lifestyle_patterns['stress_management']['max_stress']
        stress_score = max(0.0, 1.0 - (stress_level / max_stress))
        consistency_score += stress_score * self.lifestyle_patterns['stress_management']['weight']
        
        # Social support (if available)
        social_score = lifestyle.get('social_support', 5) / 10
        consistency_score += social_score * self.lifestyle_patterns['social_support']['weight']
        
        # Work-life balance (if available)
        balance_score = lifestyle.get('work_life_balance', 5) / 10
        consistency_score += balance_score * self.lifestyle_patterns['work_life_balance']['weight']
        
        # Bonus for consistency over time (if we have historical CVD scores)
        if len(cvd_scores) >= 3:
            consistency_bonus = self._calculate_consistency_bonus(cvd_scores)
            consistency_score *= (1 + consistency_bonus)
        
        return min(1.0, consistency_score)
    
    def _calculate_nutrition_quality(self, nutrition_logs: List[Dict[str, Any]]) -> float:
        """Calculate nutrition quality based on food logs and patterns"""
        if not nutrition_logs:
            return 0.5  # Default moderate score
        
        # Analyze recent nutrition (last 30 days)
        recent_logs = self._get_recent_logs(nutrition_logs, days=30)
        
        if not recent_logs:
            return 0.5
        
        # Nutrition quality metrics
        quality_metrics = {
            'heart_healthy_foods': 0,
            'processed_foods': 0,
            'high_sodium_meals': 0,
            'high_fiber_meals': 0,
            'balanced_meals': 0
        }
        
        for log in recent_logs:
            nutrition = log.get('nutrition', {})
            
            # Heart-healthy indicators
            if nutrition.get('fiber', 0) >= 5 and nutrition.get('saturated_fat', 0) <= 3:
                quality_metrics['heart_healthy_foods'] += 1
            
            # Processed food indicators
            if nutrition.get('sodium', 0) > 600 or nutrition.get('trans_fat', 0) > 0:
                quality_metrics['processed_foods'] += 1
            
            # High sodium
            if nutrition.get('sodium', 0) > 400:
                quality_metrics['high_sodium_meals'] += 1
            
            # High fiber
            if nutrition.get('fiber', 0) >= 3:
                quality_metrics['high_fiber_meals'] += 1
            
            # Balanced meal (reasonable calories, protein, fiber)
            calories = nutrition.get('calories', 0)
            protein = nutrition.get('protein', 0)
            if 200 <= calories <= 600 and protein >= 10:
                quality_metrics['balanced_meals'] += 1
        
        total_logs = len(recent_logs)
        
        # Calculate quality score
        quality_score = (
            (quality_metrics['heart_healthy_foods'] / total_logs) * 0.3 +
            (1 - quality_metrics['processed_foods'] / total_logs) * 0.25 +
            (1 - quality_metrics['high_sodium_meals'] / total_logs) * 0.2 +
            (quality_metrics['high_fiber_meals'] / total_logs) * 0.15 +
            (quality_metrics['balanced_meals'] / total_logs) * 0.1
        )
        
        return min(1.0, quality_score)
    
    def _calculate_behavioral_patterns(self, chat_sessions: List[Dict[str, Any]], user_data: Dict[str, Any]) -> float:
        """Analyze behavioral patterns from chat interactions"""
        if not chat_sessions:
            return 0.5  # Default moderate score
        
        # Analyze engagement patterns
        engagement_score = 0.0
        
        # Session frequency (regular engagement is positive)
        recent_sessions = self._get_recent_logs(chat_sessions, days=30)
        session_frequency = len(recent_sessions) / 30  # Sessions per day
        
        if session_frequency >= 0.2:  # 6+ sessions per month
            engagement_score += 0.3
        elif session_frequency >= 0.1:  # 3+ sessions per month
            engagement_score += 0.2
        else:
            engagement_score += 0.1
        
        # Health awareness (mentions of health topics)
        health_awareness = 0
        for session in recent_sessions:
            insights = session.get('insights', [])
            if any('health' in str(insight).lower() for insight in insights):
                health_awareness += 1
        
        if health_awareness > 0:
            awareness_score = min(0.3, health_awareness / len(recent_sessions))
            engagement_score += awareness_score
        
        # Proactive behavior (asking questions, seeking advice)
        proactive_score = 0.2  # Default assumption of some proactivity
        engagement_score += proactive_score
        
        # Consistency in health monitoring
        age = user_data.get('age', 30)
        if age >= 40 and session_frequency >= 0.15:
            engagement_score += 0.2  # Bonus for older adults who monitor regularly
        
        return min(1.0, engagement_score)
    
    def _calculate_environmental_factors(self, user_data: Dict[str, Any]) -> float:
        """Calculate environmental and socioeconomic factors"""
        # This is a simplified version - could be enhanced with more data
        environmental_score = 0.5  # Baseline neutral score
        
        profile = user_data.get('profile', {})
        lifestyle = profile.get('lifestyle', {})
        
        # Work stress indicator
        stress_level = lifestyle.get('stress_level', 5)
        if stress_level <= 3:
            environmental_score += 0.2  # Low stress environment
        elif stress_level >= 8:
            environmental_score -= 0.2  # High stress environment
        
        # Social support
        social_support = lifestyle.get('social_support', 5)
        if social_support >= 7:
            environmental_score += 0.15
        elif social_support <= 3:
            environmental_score -= 0.15
        
        # Access to healthcare (inferred from engagement)
        chat_sessions = user_data.get('chat_sessions', [])
        if len(chat_sessions) > 5:
            environmental_score += 0.1  # Good access to health resources
        
        return max(0.0, min(1.0, environmental_score))
    
    def _calculate_temporal_trends(self, cvd_scores: List[Dict[str, Any]], nutrition_logs: List[Dict[str, Any]]) -> float:
        """Analyze temporal trends and improvement patterns"""
        trend_score = 0.5  # Default neutral
        
        # CVD score trends
        if len(cvd_scores) >= 3:
            recent_scores = [score['score'] for score in cvd_scores[-3:]]
            if len(recent_scores) >= 2:
                # Check if trend is improving
                if recent_scores[-1] < recent_scores[0]:
                    trend_score += 0.3  # Improving trend
                elif recent_scores[-1] > recent_scores[0]:
                    trend_score -= 0.2  # Worsening trend
        
        # Nutrition improvement trends
        if len(nutrition_logs) >= 10:
            recent_nutrition = nutrition_logs[-10:]
            older_nutrition = nutrition_logs[-20:-10] if len(nutrition_logs) >= 20 else []
            
            if older_nutrition:
                recent_avg_sodium = statistics.mean([log.get('nutrition', {}).get('sodium', 0) for log in recent_nutrition])
                older_avg_sodium = statistics.mean([log.get('nutrition', {}).get('sodium', 0) for log in older_nutrition])
                
                if recent_avg_sodium < older_avg_sodium:
                    trend_score += 0.2  # Improving nutrition
        
        return max(0.0, min(1.0, trend_score))
    
    def _calculate_consistency_bonus(self, cvd_scores: List[Dict[str, Any]]) -> float:
        """Calculate bonus for consistent health monitoring"""
        if len(cvd_scores) < 3:
            return 0.0
        
        # Check regularity of assessments
        dates = []
        for score in cvd_scores:
            try:
                date = datetime.fromisoformat(score.get('calculated_at', ''))
                dates.append(date)
            except:
                continue
        
        if len(dates) < 3:
            return 0.0
        
        # Calculate average interval between assessments
        intervals = []
        for i in range(1, len(dates)):
            interval = (dates[i] - dates[i-1]).days
            intervals.append(interval)
        
        avg_interval = statistics.mean(intervals)
        
        # Bonus for regular monitoring (weekly to monthly)
        if 7 <= avg_interval <= 30:
            return 0.1
        elif 30 < avg_interval <= 60:
            return 0.05
        
        return 0.0
    
    def _get_recent_logs(self, logs: List[Dict[str, Any]], days: int = 30) -> List[Dict[str, Any]]:
        """Get logs from the last N days"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_logs = []
        
        for log in logs:
            try:
                log_date_str = log.get('logged_at') or log.get('created_at') or log.get('timestamp', '')
                log_date = datetime.fromisoformat(log_date_str)
                if log_date >= cutoff_date:
                    recent_logs.append(log)
            except:
                continue
        
        return recent_logs
    
    def _categorize_heuristic_risk(self, score: float) -> str:
        """Categorize heuristic score into risk levels"""
        if score < 0.3:
            return 'Low Risk'
        elif score < 0.5:
            return 'Low-Moderate Risk'
        elif score < 0.7:
            return 'Moderate Risk'
        elif score < 0.85:
            return 'Moderate-High Risk'
        else:
            return 'High Risk'
    
    def _generate_heuristic_insights(self, component_scores: Dict[str, float], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate insights based on heuristic analysis"""
        insights = {
            'strengths': [],
            'areas_for_improvement': [],
            'personalized_recommendations': [],
            'risk_drivers': []
        }
        
        # Identify strengths
        for component, score in component_scores.items():
            if score >= 0.7:
                insights['strengths'].append(f"Strong {component.replace('_', ' ')}")
        
        # Identify areas for improvement
        for component, score in component_scores.items():
            if score <= 0.4:
                insights['areas_for_improvement'].append(f"Improve {component.replace('_', ' ')}")
                insights['risk_drivers'].append(component)
        
        # Generate personalized recommendations
        if component_scores['lifestyle'] < 0.5:
            insights['personalized_recommendations'].append("Focus on establishing consistent daily routines")
        
        if component_scores['nutrition'] < 0.5:
            insights['personalized_recommendations'].append("Prioritize heart-healthy meal planning")
        
        if component_scores['behavioral'] < 0.5:
            insights['personalized_recommendations'].append("Increase engagement with health monitoring tools")
        
        return insights

# Convenience functions
def calculate_heuristic(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Main heuristic calculation function - wrapper for the class method"""
    engine = CVDHeuristicEngine()
    return engine.calculate_heuristic(user_data)

def quick_heuristic_assessment(age: int, family_history: bool, exercise_freq: int, stress_level: int) -> Dict[str, Any]:
    """Quick heuristic assessment with minimal data"""
    user_data = {
        'age': age,
        'profile': {
            'family_history': {'heart_attack': family_history},
            'lifestyle': {
                'exercise_frequency': exercise_freq,
                'stress_level': stress_level,
                'sleep_hours': 8
            }
        },
        'cvd_scores': [],
        'nutrition_logs': [],
        'chat_sessions': []
    }
    
    return calculate_heuristic(user_data)
