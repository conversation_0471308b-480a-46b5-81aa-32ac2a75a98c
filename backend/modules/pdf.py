"""
PDF Export Module for MIT CVD App
Generates comprehensive user health reports in PDF format
"""
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor, black, white, red, green, orange
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

# Optional chart imports
try:
    from reportlab.platypus.charts import LinePlot
    from reportlab.graphics.shapes import Drawing
    CHARTS_AVAILABLE = True
except ImportError:
    CHARTS_AVAILABLE = False
    print("Warning: ReportLab charts not available. PDF reports will not include charts.")

from config import REPORTS_DIR
from backend.models.data_models import current_timestamp

class CVDReportGenerator:
    """PDF report generator for CVD health data"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()

        # Color scheme
        self.colors = {
            'primary': HexColor('#2E86AB'),
            'secondary': HexColor('#A23B72'),
            'success': HexColor('#F18F01'),
            'warning': HexColor('#C73E1D'),
            'light_gray': HexColor('#F5F5F5'),
            'dark_gray': HexColor('#333333')
        }

        self._setup_custom_styles()
    
    def export_user_report(self, user_data: Dict[str, Any], report_type: str = 'comprehensive') -> Dict[str, Any]:
        """
        Generate comprehensive PDF report for user
        
        Args:
            user_data: Complete user data including scores, logs, etc.
            report_type: Type of report ('comprehensive', 'summary', 'progress')
            
        Returns:
            Dict with success status and file path
        """
        try:
            # Generate filename
            user_name = user_data.get('name', 'User').replace(' ', '_')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"CVD_Report_{user_name}_{timestamp}.pdf"
            filepath = os.path.join(REPORTS_DIR, filename)
            
            # Create PDF document
            doc = SimpleDocTemplate(
                filepath,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Build report content based on type
            if report_type == 'comprehensive':
                story = self._build_comprehensive_report(user_data)
            elif report_type == 'summary':
                story = self._build_summary_report(user_data)
            elif report_type == 'progress':
                story = self._build_progress_report(user_data)
            else:
                story = self._build_comprehensive_report(user_data)
            
            # Generate PDF
            doc.build(story)
            
            return {
                'success': True,
                'filepath': filepath,
                'filename': filename,
                'report_type': report_type,
                'generated_at': current_timestamp(),
                'file_size': os.path.getsize(filepath)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"PDF generation error: {str(e)}",
                'filepath': None
            }
    
    def _setup_custom_styles(self):
        """Setup custom paragraph styles"""
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=self.colors['primary']
        ))
        
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            spaceBefore=20,
            textColor=self.colors['secondary']
        ))
        
        self.styles.add(ParagraphStyle(
            name='RiskScore',
            parent=self.styles['Normal'],
            fontSize=36,
            alignment=TA_CENTER,
            spaceAfter=10
        ))
    
    def _build_comprehensive_report(self, user_data: Dict[str, Any]) -> List:
        """Build comprehensive report content"""
        story = []
        
        # Title page
        story.extend(self._create_title_page(user_data))
        story.append(PageBreak())
        
        # Executive summary
        story.extend(self._create_executive_summary(user_data))
        story.append(Spacer(1, 20))
        
        # CVD risk assessment
        story.extend(self._create_risk_assessment_section(user_data))
        story.append(Spacer(1, 20))
        
        # Lifestyle analysis
        story.extend(self._create_lifestyle_section(user_data))
        story.append(Spacer(1, 20))
        
        # Nutrition analysis
        story.extend(self._create_nutrition_section(user_data))
        story.append(PageBreak())
        
        # Progress tracking
        story.extend(self._create_progress_section(user_data))
        story.append(Spacer(1, 20))
        
        # Recommendations
        story.extend(self._create_recommendations_section(user_data))
        
        return story
    
    def _build_summary_report(self, user_data: Dict[str, Any]) -> List:
        """Build summary report content"""
        story = []
        
        # Title
        story.extend(self._create_title_page(user_data, title="CVD Health Summary"))
        story.append(Spacer(1, 30))
        
        # Key metrics
        story.extend(self._create_key_metrics_summary(user_data))
        story.append(Spacer(1, 20))
        
        # Quick recommendations
        story.extend(self._create_quick_recommendations(user_data))
        
        return story
    
    def _build_progress_report(self, user_data: Dict[str, Any]) -> List:
        """Build progress tracking report"""
        story = []
        
        # Title
        story.extend(self._create_title_page(user_data, title="CVD Progress Report"))
        story.append(Spacer(1, 30))
        
        # Progress charts and analysis
        story.extend(self._create_detailed_progress_section(user_data))
        
        return story
    
    def _create_title_page(self, user_data: Dict[str, Any], title: str = "Cardiovascular Health Report") -> List:
        """Create report title page"""
        story = []
        
        # Main title
        story.append(Paragraph(title, self.styles['CustomTitle']))
        story.append(Spacer(1, 30))
        
        # User info
        user_name = user_data.get('name', 'User')
        user_age = user_data.get('age', 'Unknown')
        report_date = datetime.now().strftime('%B %d, %Y')
        
        story.append(Paragraph(f"<b>Patient:</b> {user_name}", self.styles['Normal']))
        story.append(Paragraph(f"<b>Age:</b> {user_age}", self.styles['Normal']))
        story.append(Paragraph(f"<b>Report Date:</b> {report_date}", self.styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Disclaimer
        disclaimer = """
        <i>This report is generated by the MIT CVD App for informational purposes only. 
        It should not replace professional medical advice, diagnosis, or treatment. 
        Always consult with qualified healthcare providers regarding your health.</i>
        """
        story.append(Paragraph(disclaimer, self.styles['Normal']))
        
        return story
    
    def _create_executive_summary(self, user_data: Dict[str, Any]) -> List:
        """Create executive summary section"""
        story = []
        
        story.append(Paragraph("Executive Summary", self.styles['SectionHeader']))
        
        # Get latest CVD score
        cvd_scores = user_data.get('cvd_scores', [])
        if cvd_scores:
            latest_score = cvd_scores[-1]
            risk_level = latest_score.get('risk_level', 'Unknown')
            score_value = latest_score.get('score', 0)
            
            # Risk score display
            score_color = self._get_risk_color(risk_level)
            story.append(Paragraph(
                f'<font color="{score_color}"><b>Current Risk Level: {risk_level}</b></font>',
                self.styles['Normal']
            ))
            story.append(Paragraph(
                f'<font color="{score_color}" size="24"><b>{score_value:.1%}</b></font>',
                self.styles['RiskScore']
            ))
        
        # Key insights
        summary_text = self._generate_summary_text(user_data)
        story.append(Paragraph(summary_text, self.styles['Normal']))
        
        return story
    
    def _create_risk_assessment_section(self, user_data: Dict[str, Any]) -> List:
        """Create CVD risk assessment section"""
        story = []
        
        story.append(Paragraph("CVD Risk Assessment", self.styles['SectionHeader']))
        
        cvd_scores = user_data.get('cvd_scores', [])
        if not cvd_scores:
            story.append(Paragraph("No CVD assessments available.", self.styles['Normal']))
            return story
        
        latest_score = cvd_scores[-1]
        
        # Risk factors table
        risk_factors = latest_score.get('factors', {}).get('risk_factors', [])
        protective_factors = latest_score.get('factors', {}).get('protective_factors', [])
        
        # Create risk factors table
        table_data = [['Risk Factors', 'Protective Factors']]
        max_rows = max(len(risk_factors), len(protective_factors))
        
        for i in range(max_rows):
            risk_factor = risk_factors[i] if i < len(risk_factors) else ''
            protective_factor = protective_factors[i] if i < len(protective_factors) else ''
            table_data.append([risk_factor, protective_factor])
        
        table = Table(table_data, colWidths=[3*inch, 3*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), self.colors['light_gray']),
            ('TEXTCOLOR', (0, 0), (-1, 0), black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), white),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        story.append(table)
        
        return story
    
    def _create_lifestyle_section(self, user_data: Dict[str, Any]) -> List:
        """Create lifestyle analysis section"""
        story = []
        
        story.append(Paragraph("Lifestyle Analysis", self.styles['SectionHeader']))
        
        profile = user_data.get('profile', {})
        lifestyle = profile.get('lifestyle', {})
        
        if not lifestyle:
            story.append(Paragraph("No lifestyle data available.", self.styles['Normal']))
            return story
        
        # Lifestyle metrics table
        lifestyle_data = [
            ['Metric', 'Current Value', 'Recommendation'],
            ['Exercise Frequency', f"{lifestyle.get('exercise_frequency', 0)} times/week", '≥3 times/week'],
            ['Sleep Hours', f"{lifestyle.get('sleep_hours', 8)} hours", '7-8 hours'],
            ['Stress Level', f"{lifestyle.get('stress_level', 5)}/10", '≤5/10'],
            ['Smoking Status', 'Yes' if lifestyle.get('smoking') else 'No', 'No']
        ]
        
        table = Table(lifestyle_data, colWidths=[2*inch, 1.5*inch, 2*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), self.colors['light_gray']),
            ('TEXTCOLOR', (0, 0), (-1, 0), black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), white),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        story.append(table)
        
        return story
    
    def _create_nutrition_section(self, user_data: Dict[str, Any]) -> List:
        """Create nutrition analysis section"""
        story = []
        
        story.append(Paragraph("Nutrition Analysis", self.styles['SectionHeader']))
        
        nutrition_logs = user_data.get('nutrition_logs', [])
        
        if not nutrition_logs:
            story.append(Paragraph("No nutrition data available.", self.styles['Normal']))
            return story
        
        # Analyze recent nutrition (last 7 days)
        recent_logs = nutrition_logs[-7:] if len(nutrition_logs) >= 7 else nutrition_logs
        
        # Calculate averages
        avg_calories = sum(log.get('nutrition', {}).get('calories', 0) for log in recent_logs) / len(recent_logs)
        avg_sodium = sum(log.get('nutrition', {}).get('sodium', 0) for log in recent_logs) / len(recent_logs)
        avg_fiber = sum(log.get('nutrition', {}).get('fiber', 0) for log in recent_logs) / len(recent_logs)
        
        nutrition_summary = f"""
        Based on your recent {len(recent_logs)} meal logs:
        • Average calories per meal: {avg_calories:.0f}
        • Average sodium per meal: {avg_sodium:.0f}mg
        • Average fiber per meal: {avg_fiber:.1f}g
        """
        
        story.append(Paragraph(nutrition_summary, self.styles['Normal']))
        
        return story
    
    def _create_progress_section(self, user_data: Dict[str, Any]) -> List:
        """Create progress tracking section"""
        story = []
        
        story.append(Paragraph("Progress Tracking", self.styles['SectionHeader']))
        
        cvd_scores = user_data.get('cvd_scores', [])
        
        if len(cvd_scores) < 2:
            story.append(Paragraph("Insufficient data for progress analysis. Complete more assessments to see trends.", self.styles['Normal']))
            return story
        
        # Progress analysis
        first_score = cvd_scores[0]['score']
        latest_score = cvd_scores[-1]['score']
        change = latest_score - first_score
        
        if change < -0.05:
            progress_text = f"<font color='green'><b>Improvement:</b> Your CVD risk has decreased by {abs(change):.1%}</font>"
        elif change > 0.05:
            progress_text = f"<font color='red'><b>Attention:</b> Your CVD risk has increased by {change:.1%}</font>"
        else:
            progress_text = "<font color='orange'><b>Stable:</b> Your CVD risk has remained relatively stable</font>"
        
        story.append(Paragraph(progress_text, self.styles['Normal']))
        
        return story
    
    def _create_recommendations_section(self, user_data: Dict[str, Any]) -> List:
        """Create recommendations section"""
        story = []
        
        story.append(Paragraph("Personalized Recommendations", self.styles['SectionHeader']))
        
        # Get recommendations from latest CVD score
        cvd_scores = user_data.get('cvd_scores', [])
        if cvd_scores:
            recommendations = cvd_scores[-1].get('recommendations', [])
            
            for i, rec in enumerate(recommendations, 1):
                story.append(Paragraph(f"{i}. {rec}", self.styles['Normal']))
                story.append(Spacer(1, 6))
        else:
            # Default recommendations
            default_recs = [
                "Complete your first CVD risk assessment",
                "Log your daily meals for better nutrition tracking",
                "Maintain regular exercise routine",
                "Monitor your health metrics regularly"
            ]
            
            for i, rec in enumerate(default_recs, 1):
                story.append(Paragraph(f"{i}. {rec}", self.styles['Normal']))
                story.append(Spacer(1, 6))
        
        return story
    
    def _create_key_metrics_summary(self, user_data: Dict[str, Any]) -> List:
        """Create key metrics summary for summary report"""
        story = []
        
        # Quick stats table
        cvd_scores = user_data.get('cvd_scores', [])
        nutrition_logs = user_data.get('nutrition_logs', [])
        
        current_risk = cvd_scores[-1]['risk_level'] if cvd_scores else 'Not assessed'
        total_assessments = len(cvd_scores)
        total_nutrition_logs = len(nutrition_logs)
        
        metrics_data = [
            ['Metric', 'Value'],
            ['Current Risk Level', current_risk],
            ['Total Assessments', str(total_assessments)],
            ['Nutrition Logs', str(total_nutrition_logs)],
            ['Account Age', self._calculate_account_age(user_data)]
        ]
        
        table = Table(metrics_data, colWidths=[3*inch, 2*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), self.colors['light_gray']),
            ('TEXTCOLOR', (0, 0), (-1, 0), black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        story.append(table)
        
        return story
    
    def _create_quick_recommendations(self, user_data: Dict[str, Any]) -> List:
        """Create quick recommendations for summary report"""
        story = []
        
        story.append(Paragraph("Top 3 Recommendations", self.styles['SectionHeader']))
        
        # Get top 3 recommendations
        cvd_scores = user_data.get('cvd_scores', [])
        if cvd_scores:
            recommendations = cvd_scores[-1].get('recommendations', [])[:3]
        else:
            recommendations = [
                "Complete your first CVD risk assessment",
                "Start logging your daily meals",
                "Establish a regular exercise routine"
            ]
        
        for i, rec in enumerate(recommendations, 1):
            story.append(Paragraph(f"<b>{i}.</b> {rec}", self.styles['Normal']))
            story.append(Spacer(1, 8))
        
        return story
    
    def _create_detailed_progress_section(self, user_data: Dict[str, Any]) -> List:
        """Create detailed progress section for progress report"""
        story = []
        
        story.append(Paragraph("Detailed Progress Analysis", self.styles['SectionHeader']))
        
        # This would include charts and detailed trend analysis
        # For now, we'll include text-based analysis
        
        cvd_scores = user_data.get('cvd_scores', [])
        if len(cvd_scores) >= 2:
            # Trend analysis
            scores = [score['score'] for score in cvd_scores]
            dates = [score.get('calculated_at', '') for score in cvd_scores]
            
            trend_text = f"""
            Progress Summary:
            • First assessment: {scores[0]:.1%} risk
            • Latest assessment: {scores[-1]:.1%} risk
            • Total assessments: {len(scores)}
            • Assessment period: {len(cvd_scores)} evaluations
            """
            
            story.append(Paragraph(trend_text, self.styles['Normal']))
        
        return story
    
    def _get_risk_color(self, risk_level: str) -> str:
        """Get color for risk level"""
        color_map = {
            'Low': 'green',
            'Moderate': 'orange',
            'High': 'red',
            'Very High': 'darkred'
        }
        return color_map.get(risk_level, 'black')
    
    def _generate_summary_text(self, user_data: Dict[str, Any]) -> str:
        """Generate summary text for executive summary"""
        cvd_scores = user_data.get('cvd_scores', [])
        nutrition_logs = user_data.get('nutrition_logs', [])
        
        if not cvd_scores:
            return "Complete your first CVD risk assessment to see personalized insights and recommendations."
        
        latest_score = cvd_scores[-1]
        risk_level = latest_score.get('risk_level', 'Unknown')
        
        summary = f"Your current cardiovascular risk level is {risk_level}. "
        
        if len(cvd_scores) > 1:
            change = cvd_scores[-1]['score'] - cvd_scores[-2]['score']
            if change < -0.05:
                summary += "Your risk has improved since your last assessment. "
            elif change > 0.05:
                summary += "Your risk has increased since your last assessment. "
            else:
                summary += "Your risk level has remained stable. "
        
        summary += f"You have completed {len(cvd_scores)} risk assessments and logged {len(nutrition_logs)} meals."
        
        return summary
    
    def _calculate_account_age(self, user_data: Dict[str, Any]) -> str:
        """Calculate how long the user has been using the app"""
        created_at = user_data.get('created_at')
        if not created_at:
            return 'Unknown'
        
        try:
            created_date = datetime.fromisoformat(created_at)
            age_days = (datetime.now() - created_date).days
            
            if age_days < 7:
                return f"{age_days} days"
            elif age_days < 30:
                return f"{age_days // 7} weeks"
            else:
                return f"{age_days // 30} months"
        except:
            return 'Unknown'

# Convenience functions
def export_user_report(user_data: Dict[str, Any], report_type: str = 'comprehensive') -> Dict[str, Any]:
    """Main PDF export function - wrapper for the class method"""
    generator = CVDReportGenerator()
    return generator.export_user_report(user_data, report_type)

def quick_summary_report(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Generate a quick summary report"""
    return export_user_report(user_data, 'summary')

def progress_report(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Generate a progress tracking report"""
    return export_user_report(user_data, 'progress')
