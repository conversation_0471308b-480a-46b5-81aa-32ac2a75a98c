from backend.infra.connection_openai import ConnectionOpenAI

class TextCompletation:

    def completion(self, prompt: str) -> str:
        client = ConnectionOpenAI.client()
        response = client.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        }
                    ]
                }
            ],
            #response_format={"type": "json_object"}
        )
        return response.choices[0].message.content