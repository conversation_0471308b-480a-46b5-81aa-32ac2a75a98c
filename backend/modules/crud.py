"""
CRUD Operations Module for MIT CVD App
Handles all JSON database operations for users, sessions, and streaks
Enhanced with validation and error handling
"""
import json
import os
import shutil
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from config import USERS_DB_PATH, SESSIONS_DB_PATH, STREAKS_DB_PATH, DATA_DIR
from backend.models.data_models import UserModel, SessionModel, StreakModel, current_timestamp

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JSONDatabase:
    """Enhanced JSON file database operations with validation"""

    @staticmethod
    def load_data(file_path: str) -> List[Dict[str, Any]]:
        """Load data from JSON file with error recovery"""
        if not os.path.exists(file_path):
            logger.info(f"Database file {file_path} does not exist, creating empty database")
            return []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if not isinstance(data, list):
                    logger.warning(f"Database file {file_path} contains non-list data, resetting to empty list")
                    return []
                return data
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error in {file_path}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error loading {file_path}: {e}")
            return []

    @staticmethod
    def save_data(file_path: str, data: List[Dict[str, Any]]) -> bool:
        """Save data to JSON file with validation"""
        try:
            # Validate data structure
            if not isinstance(data, list):
                logger.error(f"Invalid data type for {file_path}: expected list, got {type(data)}")
                return False

            # Validate each item in the list
            for i, item in enumerate(data):
                if not isinstance(item, dict):
                    logger.error(f"Invalid item type at index {i} in {file_path}: expected dict, got {type(item)}")
                    return False

            # Write to temporary file first
            temp_path = f"{file_path}.tmp"
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            # Atomic move to final location
            shutil.move(temp_path, file_path)
            logger.debug(f"Successfully saved data to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving data to {file_path}: {e}")
            # Clean up temporary file if it exists
            temp_path = f"{file_path}.tmp"
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except OSError:
                    pass
            return False

    @staticmethod
    def validate_user_data(user_data: Dict[str, Any]) -> bool:
        """Validate user data structure"""
        required_fields = ['id', 'name', 'email', 'created_at']
        return all(field in user_data for field in required_fields)

    @staticmethod
    def validate_session_data(session_data: Dict[str, Any]) -> bool:
        """Validate session data structure"""
        required_fields = ['id', 'user_id', 'type', 'created_at']
        return all(field in session_data for field in required_fields)

    @staticmethod
    def validate_streak_data(streak_data: Dict[str, Any]) -> bool:
        """Validate streak data structure"""
        required_fields = ['id', 'user_id', 'category', 'current_streak', 'created_at']
        return all(field in streak_data for field in required_fields)

    @staticmethod
    def get_database_stats() -> Dict[str, Dict[str, Any]]:
        """Get statistics for all database files"""
        stats = {}
        for db_name, db_path in [
            ('users', USERS_DB_PATH),
            ('sessions', SESSIONS_DB_PATH),
            ('streaks', STREAKS_DB_PATH)
        ]:
            if os.path.exists(db_path):
                try:
                    data = JSONDatabase.load_data(db_path)
                    stats[db_name] = {
                        'record_count': len(data),
                        'file_size': os.path.getsize(db_path),
                        'last_modified': datetime.fromtimestamp(os.path.getmtime(db_path)).isoformat()
                    }
                except Exception as e:
                    stats[db_name] = {
                        'error': str(e),
                        'record_count': 0,
                        'file_size': 0,
                        'last_modified': None
                    }
            else:
                stats[db_name] = {
                    'record_count': 0,
                    'file_size': 0,
                    'last_modified': None,
                    'exists': False
                }
        return stats

# User Management
def create_user(user_data: Dict[str, Any]) -> bool:
    """Create a new user with validation"""
    try:
        # Validate required fields
        if not JSONDatabase.validate_user_data(user_data):
            logger.error(f"Invalid user data structure: {user_data}")
            return False

        users = JSONDatabase.load_data(USERS_DB_PATH)

        # Check for duplicate email
        if any(user['email'] == user_data['email'] for user in users):
            logger.error(f"User with email {user_data['email']} already exists")
            return False

        # Add creation timestamp if not provided
        if 'created_at' not in user_data:
            user_data['created_at'] = current_timestamp()

        users.append(user_data)
        return JSONDatabase.save_data(USERS_DB_PATH, users)

    except Exception as e:
        logger.error(f"Error creating user: {e}")
        return False

def get_user(user_id: str) -> Optional[Dict[str, Any]]:
    """Get user by ID"""
    try:
        users = JSONDatabase.load_data(USERS_DB_PATH)
        return next((user for user in users if user['id'] == user_id), None)
    except Exception as e:
        logger.error(f"Error getting user {user_id}: {e}")
        return None

def get_user_by_email(email: str) -> Optional[Dict[str, Any]]:
    """Get user by email"""
    try:
        users = JSONDatabase.load_data(USERS_DB_PATH)
        return next((user for user in users if user['email'] == email), None)
    except Exception as e:
        logger.error(f"Error getting user by email {email}: {e}")
        return None

def update_user(user_id: str, updates: Dict[str, Any]) -> bool:
    """Update user data"""
    try:
        users = JSONDatabase.load_data(USERS_DB_PATH)
        
        for i, user in enumerate(users):
            if user['id'] == user_id:
                users[i].update(updates)
                users[i]['updated_at'] = current_timestamp()
                return JSONDatabase.save_data(USERS_DB_PATH, users)
        
        logger.error(f"User {user_id} not found for update")
        return False

    except Exception as e:
        logger.error(f"Error updating user {user_id}: {e}")
        return False

def delete_user(user_id: str) -> bool:
    """Delete user and all associated data"""
    try:
        # Delete user
        users = JSONDatabase.load_data(USERS_DB_PATH)
        users = [user for user in users if user['id'] != user_id]
        
        # Delete user sessions
        sessions = JSONDatabase.load_data(SESSIONS_DB_PATH)
        sessions = [session for session in sessions if session['user_id'] != user_id]
        
        # Delete user streaks
        streaks = JSONDatabase.load_data(STREAKS_DB_PATH)
        streaks = [streak for streak in streaks if streak['user_id'] != user_id]
        
        # Save all changes
        return (JSONDatabase.save_data(USERS_DB_PATH, users) and
                JSONDatabase.save_data(SESSIONS_DB_PATH, sessions) and
                JSONDatabase.save_data(STREAKS_DB_PATH, streaks))

    except Exception as e:
        logger.error(f"Error deleting user {user_id}: {e}")
        return False

def list_users() -> List[Dict[str, Any]]:
    """List all users"""
    try:
        return JSONDatabase.load_data(USERS_DB_PATH)
    except Exception as e:
        logger.error(f"Error listing users: {e}")
        return []

# Session Management
def create_session(session_data: Dict[str, Any]) -> bool:
    """Create a new session with validation"""
    try:
        if not JSONDatabase.validate_session_data(session_data):
            logger.error(f"Invalid session data structure: {session_data}")
            return False

        sessions = JSONDatabase.load_data(SESSIONS_DB_PATH)
        
        if 'created_at' not in session_data:
            session_data['created_at'] = current_timestamp()

        sessions.append(session_data)
        return JSONDatabase.save_data(SESSIONS_DB_PATH, sessions)

    except Exception as e:
        logger.error(f"Error creating session: {e}")
        return False

def get_session(session_id: str) -> Optional[Dict[str, Any]]:
    """Get session by ID"""
    try:
        sessions = JSONDatabase.load_data(SESSIONS_DB_PATH)
        return next((session for session in sessions if session['id'] == session_id), None)
    except Exception as e:
        logger.error(f"Error getting session {session_id}: {e}")
        return None

def get_user_sessions(user_id: str) -> List[Dict[str, Any]]:
    """Get all sessions for a user"""
    try:
        sessions = JSONDatabase.load_data(SESSIONS_DB_PATH)
        return [session for session in sessions if session['user_id'] == user_id]
    except Exception as e:
        logger.error(f"Error getting sessions for user {user_id}: {e}")
        return []

def update_session(session_id: str, updates: Dict[str, Any]) -> bool:
    """Update session data"""
    try:
        sessions = JSONDatabase.load_data(SESSIONS_DB_PATH)
        
        for i, session in enumerate(sessions):
            if session['id'] == session_id:
                sessions[i].update(updates)
                sessions[i]['updated_at'] = current_timestamp()
                return JSONDatabase.save_data(SESSIONS_DB_PATH, sessions)
        
        logger.error(f"Session {session_id} not found for update")
        return False

    except Exception as e:
        logger.error(f"Error updating session {session_id}: {e}")
        return False

def delete_session(session_id: str) -> bool:
    """Delete session"""
    try:
        sessions = JSONDatabase.load_data(SESSIONS_DB_PATH)
        original_count = len(sessions)
        sessions = [session for session in sessions if session['id'] != session_id]
        
        if len(sessions) == original_count:
            logger.error(f"Session {session_id} not found for deletion")
            return False
        
        return JSONDatabase.save_data(SESSIONS_DB_PATH, sessions)

    except Exception as e:
        logger.error(f"Error deleting session {session_id}: {e}")
        return False

# Streak Management
def create_streak(streak_data: Dict[str, Any]) -> bool:
    """Create a new streak with validation"""
    try:
        if not JSONDatabase.validate_streak_data(streak_data):
            logger.error(f"Invalid streak data structure: {streak_data}")
            return False

        streaks = JSONDatabase.load_data(STREAKS_DB_PATH)
        
        if 'created_at' not in streak_data:
            streak_data['created_at'] = current_timestamp()

        streaks.append(streak_data)
        return JSONDatabase.save_data(STREAKS_DB_PATH, streaks)

    except Exception as e:
        logger.error(f"Error creating streak: {e}")
        return False

def get_streak(streak_id: str) -> Optional[Dict[str, Any]]:
    """Get streak by ID"""
    try:
        streaks = JSONDatabase.load_data(STREAKS_DB_PATH)
        return next((streak for streak in streaks if streak['id'] == streak_id), None)
    except Exception as e:
        logger.error(f"Error getting streak {streak_id}: {e}")
        return None

def get_user_streaks(user_id: str) -> List[Dict[str, Any]]:
    """Get all streaks for a user"""
    try:
        streaks = JSONDatabase.load_data(STREAKS_DB_PATH)
        return [streak for streak in streaks if streak['user_id'] == user_id]
    except Exception as e:
        logger.error(f"Error getting streaks for user {user_id}: {e}")
        return []

def update_streak(streak_id: str, updates: Dict[str, Any]) -> bool:
    """Update streak data"""
    try:
        streaks = JSONDatabase.load_data(STREAKS_DB_PATH)
        
        for i, streak in enumerate(streaks):
            if streak['id'] == streak_id:
                streaks[i].update(updates)
                streaks[i]['updated_at'] = current_timestamp()
                return JSONDatabase.save_data(STREAKS_DB_PATH, streaks)
        
        logger.error(f"Streak {streak_id} not found for update")
        return False

    except Exception as e:
        logger.error(f"Error updating streak {streak_id}: {e}")
        return False

def delete_streak(streak_id: str) -> bool:
    """Delete streak"""
    try:
        streaks = JSONDatabase.load_data(STREAKS_DB_PATH)
        original_count = len(streaks)
        streaks = [streak for streak in streaks if streak['id'] != streak_id]
        
        if len(streaks) == original_count:
            logger.error(f"Streak {streak_id} not found for deletion")
            return False
        
        return JSONDatabase.save_data(STREAKS_DB_PATH, streaks)

    except Exception as e:
        logger.error(f"Error deleting streak {streak_id}: {e}")
        return False

# Nutrition Log Management
def add_nutrition_log(user_id: str, nutrition_data: Dict[str, Any]) -> bool:
    """Add nutrition log entry to user"""
    try:
        user = get_user(user_id)
        if not user:
            logger.error(f"User {user_id} not found")
            return False

        if 'nutrition_logs' not in user:
            user['nutrition_logs'] = []

        # Add timestamp if not provided
        if 'timestamp' not in nutrition_data:
            nutrition_data['timestamp'] = current_timestamp()

        user['nutrition_logs'].append(nutrition_data)
        return update_user(user_id, {'nutrition_logs': user['nutrition_logs']})

    except Exception as e:
        logger.error(f"Error adding nutrition log to user {user_id}: {e}")
        return False

# Database Management Utilities
def get_database_health() -> Dict[str, Any]:
    """Get comprehensive database health information"""
    try:
        health_info = {
            'status': 'healthy',
            'stats': JSONDatabase.get_database_stats(),
            'issues': [],
            'recommendations': []
        }

        # Check for issues
        for db_name, stats in health_info['stats'].items():
            if stats['record_count'] == 0 and db_name == 'users':
                health_info['issues'].append("No users in database")
                health_info['status'] = 'warning'

            if stats['file_size'] > 10 * 1024 * 1024:  # 10MB
                health_info['issues'].append(f"{db_name} database is large ({stats['file_size']} bytes)")
                health_info['recommendations'].append(f"Consider archiving old {db_name} data")

        if health_info['issues']:
            health_info['status'] = 'warning' if health_info['status'] == 'healthy' else health_info['status']

        return health_info

    except Exception as e:
        logger.error(f"Error checking database health: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'stats': {},
            'issues': ['Failed to check database health'],
            'recommendations': ['Check database file permissions and disk space']
        }
