import os
from dotenv import load_dotenv
from langchain.docstore.document import Document
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import CharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from typing import List, Tuple, Optional

from langchain_iris import IRISVector
load_dotenv()

class VectorSearch:
    # Constants
    NO_CONNECTION_ERROR = "No vector store connection. Connect to an existing store first."
    
    def __init__(self, collection_name: str = "cvd_app_docs"):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.intersystem_username = os.getenv("INTERSYSTEM_USERNAME")
        self.intersystem_password = os.getenv("INTERSYSTEM_PASSWORD")
        self.hostname = os.getenv("IRIS_HOSTNAME")
        self.port = os.getenv("IRIS_PORT")
        self.namespace = os.getenv("IRIS_NAMESPACE")
        
        # Collection name cannot have '.' in the name
        self.collection_name = collection_name.replace('.', '_')
        
        # Initialize embeddings
        self.embeddings = OpenAIEmbeddings()
        
        # Connection string
        self.connection_string = self.connection_iris()
        
        # Vector store instance
        self.db = None
        
    def connection_iris(self):
        username = self.intersystem_username
        password = self.intersystem_password
        hostname = self.hostname
        port = self.port
        namespace = self.namespace
        CONNECTION_STRING = f"iris://{username}:{password}@{hostname}:{port}/{namespace}"
        return CONNECTION_STRING

    def load_and_split_documents(self, path_text: str):
        """Load and split documents from a text file"""
        loader = TextLoader(path_text, encoding='utf-8')
        documents = loader.load()
        text_splitter = CharacterTextSplitter(chunk_size=400, chunk_overlap=20)
        docs = text_splitter.split_documents(documents)
        return docs
    
    def create_vector_store(self, documents: List[Document]):
        """Create a persistent vector store from documents"""
        try:
            # This creates a persistent vector store (a SQL table). Run this ONCE only
            self.db = IRISVector.from_documents(
                embedding=self.embeddings,
                documents=documents,
                collection_name=self.collection_name,
                connection_string=self.connection_string,
            )
            print(f"Vector store created successfully with {len(self.db.get()['ids'])} documents")
            return True
        except Exception as e:
            print(f"Error creating vector store: {e}")
            return False
    
    def connect_to_existing_vector_store(self):
        """Connect to an existing vector store"""
        try:
            self.db = IRISVector(
                embedding_function=self.embeddings,
                dimension=1536,
                collection_name=self.collection_name,
                connection_string=self.connection_string,
            )
            print("Connected to existing vector store successfully")
            return True
        except Exception as e:
            print(f"Error connecting to vector store: {e}")
            return False
    
    def add_documents(self, documents: List[Document]):
        """Add documents to an existing vector store"""
        if self.db is None:
            print(self.NO_CONNECTION_ERROR)
            return False
        
        try:
            self.db.add_documents(documents)
            print(f"Added {len(documents)} documents to vector store")
            return True
        except Exception as e:
            print(f"Error adding documents: {e}")
            return False
    
    def add_text_documents(self, texts: List[str]):
        """Add text documents to the vector store"""
        documents = [Document(page_content=text) for text in texts]
        return self.add_documents(documents)
    
    def similarity_search(self, query: str, k: int = 4) -> List[Document]:
        """Perform similarity search and return documents"""
        if self.db is None:
            print(self.NO_CONNECTION_ERROR)
            return []
        
        try:
            results = self.db.similarity_search(query, k=k)
            return results
        except Exception as e:
            print(f"Error performing similarity search: {e}")
            return []
    
    def similarity_search_with_score(self, query: str, k: int = 4) -> List[Tuple[Document, float]]:
        """Perform similarity search and return documents with scores"""
        if self.db is None:
            print(self.NO_CONNECTION_ERROR)
            return []
        
        try:
            results = self.db.similarity_search_with_score(query, k=k)
            return results
        except Exception as e:
            print(f"Error performing similarity search with score: {e}")
            return []
    
    def get_vector_store_info(self):
        """Get information about the vector store"""
        if self.db is None:
            print("No vector store connection.")
            return None
        
        try:
            info = self.db.get()
            return {
                'total_documents': len(info['ids']),
                'collection_name': self.collection_name,
                'connection_string': self.connection_string
            }
        except Exception as e:
            print(f"Error getting vector store info: {e}")
            return None
    
    def setup_from_file(self, file_path: str, create_new: bool = True):
        """Complete setup from a text file"""
        # Load and split documents
        docs = self.load_and_split_documents(file_path)
        
        if create_new:
            # Create new vector store
            success = self.create_vector_store(docs)
        else:
            # Connect to existing and add documents
            if self.connect_to_existing_vector_store():
                success = self.add_documents(docs)
            else:
                success = False
        
        return success
    
    def search_and_display_results(self, query: str, k: int = 2, show_scores: bool = True):
        """Search and display results in a formatted way"""
        if show_scores:
            results = self.similarity_search_with_score(query, k)
            print(f"\nSearch results for query: '{query}'\n")
            for i, (doc, score) in enumerate(results, 1):
                print("-" * 80)
                print(f"Result {i} - Score: {score}")
                print(doc.page_content)
                print("-" * 80)
        else:
            results = self.similarity_search(query, k)
            print(f"\nSearch results for query: '{query}'\n")
            for i, doc in enumerate(results, 1):
                print("-" * 80)
                print(f"Result {i}")
                print(doc.page_content)
                print("-" * 80)
        
        return results
    
    
#test 
if __name__ == "__main__":
    # Import config for test file path
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from config import TEST_FILE_PATH
    
    # Initialize vector search
    vs = VectorSearch("cvd_test_collection")
    
    # Test connection
    print("Connection string:", vs.connection_iris())
    # doc = VectorSearch().load_and_split_documents(TEST_FILE_PATH)
    # print(VectorSearch().create_vector_store(doc))
    # print(VectorSearch().similarity_search("aqqq", k=2))
