"""
CVD Risk Score Calculation Module for MIT CVD App
Scientifically-based cardiovascular risk assessment using the Framingham Risk Score
Based on D'Agostino RB Sr, et al. "General cardiovascular risk profile for use in primary care."
Circulation. 2008;117(6):743-53.
"""
import math
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from backend.models.data_models import CVDScoreModel, current_timestamp

class FraminghamRiskCalculator:
    """
    Framingham Risk Score Calculator for 10-year CVD Risk Assessment

    Based on D'Agostino RB Sr, et al. "General cardiovascular risk profile for use in primary care."
    Circulation. 2008;117(6):743-53.

    Calculates 10-year risk of cardiovascular disease including:
    - Coronary death, myocardial infarction, coronary insufficiency, angina
    - Ischemic stroke, hemorrhagic stroke, transient ischemic attack
    - Peripheral artery disease, heart failure
    """

    def __init__(self):
        # Framingham Risk Score Coefficients for Men
        self.male_coefficients = {
            'ln_age': 3.06117,
            'ln_total_cholesterol': 1.12370,
            'ln_hdl': -0.93263,
            'ln_treated_sbp': 1.93303,
            'ln_untreated_sbp': 1.99881,
            'smoking': 0.65451,
            'diabetes': 0.57367
        }

        # Framingham Risk Score Coefficients for Women
        self.female_coefficients = {
            'ln_age': 2.32888,
            'ln_total_cholesterol': 1.20904,
            'ln_hdl': -0.70833,
            'ln_treated_sbp': 2.76157,
            'ln_untreated_sbp': 2.82263,
            'smoking': 0.52873,
            'diabetes': 0.69154
        }

        # Baseline survival rates and mean coefficients
        self.male_baseline_survival = 0.88936
        self.male_mean_coefficient = 23.9802

        self.female_baseline_survival = 0.95012
        self.female_mean_coefficient = 26.1931

        # Risk categories based on 2019 AHA/ACC Guidelines
        self.risk_categories = {
            'low': {'threshold': 0.05, 'label': 'Low', 'description': '<5% 10-year risk'},
            'borderline': {'threshold': 0.075, 'label': 'Borderline', 'description': '5-7.4% 10-year risk'},
            'intermediate': {'threshold': 0.20, 'label': 'Intermediate', 'description': '7.5-19.9% 10-year risk'},
            'high': {'threshold': 1.0, 'label': 'High', 'description': '≥20% 10-year risk'}
        }

        # Additional risk factors with evidence-based multipliers
        self.family_history_multipliers = {
            'coronary_heart_disease': 1.40,  # 40% increased risk
            'stroke': 1.30,                  # 30% increased risk
            'hypertension': 1.25,           # 25% increased risk
            'diabetes': 1.35,               # 35% increased risk
            'hyperlipidemia': 1.20          # 20% increased risk
        }
    
    def calculate_cvd_risk(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate 10-year CVD risk using Framingham Risk Score

        Args:
            user_data: User profile data with clinical parameters

        Returns:
            Dict with Framingham risk score, level, factors, and recommendations
        """
        try:
            # Extract required clinical parameters
            age = user_data.get('age')
            gender = user_data.get('gender', 'male')  # 'male' or 'female'
            profile = user_data.get('profile', {})
            clinical = profile.get('clinical', {})
            lifestyle = profile.get('lifestyle', {})

            # Required Framingham parameters
            total_cholesterol = clinical.get('total_cholesterol')
            hdl_cholesterol = clinical.get('hdl_cholesterol')
            systolic_bp = clinical.get('systolic_blood_pressure')
            on_bp_medication = clinical.get('hypertension_treatment', False)
            smoking = lifestyle.get('smoking', False)
            diabetes = clinical.get('diabetes', False)

            # Validate required parameters
            validation_result = self._validate_framingham_parameters(
                age, total_cholesterol, hdl_cholesterol, systolic_bp
            )

            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': f"Missing required clinical data: {validation_result['missing']}",
                    'score': None,
                    'risk_level': 'Insufficient Data',
                    'requires_clinical_data': True
                }

            # Calculate Framingham Risk Score
            framingham_risk = self._calculate_framingham_risk(
                age=age,
                gender=gender,
                total_cholesterol=total_cholesterol,
                hdl_cholesterol=hdl_cholesterol,
                systolic_bp=systolic_bp,
                on_bp_medication=on_bp_medication,
                smoking=smoking,
                diabetes=diabetes
            )

            # Apply family history modifiers if available
            family_history = profile.get('family_history', {})
            adjusted_risk = self._apply_family_history_adjustment(framingham_risk, family_history)
            
            # Apply lifestyle modifiers for comprehensive risk assessment
            lifestyle_adjusted_risk = self._apply_lifestyle_adjustments(adjusted_risk, lifestyle)

            # Determine risk category
            risk_category = self._get_risk_category(lifestyle_adjusted_risk)

            # Generate clinical recommendations
            risk_factors = self._identify_framingham_risk_factors(user_data)
            protective_factors = self._identify_protective_factors(user_data)
            recommendations = self._generate_clinical_recommendations(lifestyle_adjusted_risk, risk_factors, user_data)

            # Create score record
            score_data = CVDScoreModel.create_score(
                user_data.get('id', 'unknown'),
                lifestyle_adjusted_risk,
                {
                    'framingham_risk': framingham_risk,
                    'family_adjusted_risk': adjusted_risk,
                    'lifestyle_adjusted_risk': lifestyle_adjusted_risk,
                    'adjusted_risk': adjusted_risk,  # For backwards compatibility
                    'risk_category': risk_category,
                    'clinical_parameters': {
                        'age': age,
                        'gender': gender,
                        'total_cholesterol': total_cholesterol,
                        'hdl_cholesterol': hdl_cholesterol,
                        'systolic_bp': systolic_bp,
                        'on_bp_medication': on_bp_medication,
                        'smoking': smoking,
                        'diabetes': diabetes
                    },
                    'risk_factors': risk_factors,
                    'protective_factors': protective_factors
                }
            )

            return {
                'success': True,
                'score': lifestyle_adjusted_risk,
                'risk_level': risk_category['label'],
                'risk_category': risk_category,
                'percentage': round(lifestyle_adjusted_risk * 100, 1),
                'framingham_risk': framingham_risk,
                'family_adjusted_risk': adjusted_risk,
                'lifestyle_adjusted_risk': lifestyle_adjusted_risk,
                'risk_factors': risk_factors,
                'protective_factors': protective_factors,
                'recommendations': recommendations,
                'clinical_parameters': {
                    'age': age,
                    'gender': gender,
                    'total_cholesterol': total_cholesterol,
                    'hdl_cholesterol': hdl_cholesterol,
                    'systolic_bp': systolic_bp,
                    'on_bp_medication': on_bp_medication,
                    'smoking': smoking,
                    'diabetes': diabetes
                },
                'score_data': score_data,
                'calculation_timestamp': current_timestamp(),
                'methodology': 'Comprehensive CVD Risk (Framingham + Family History + Lifestyle Factors)'
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"Framingham CVD risk calculation error: {str(e)}",
                'score': None,
                'risk_level': 'Calculation Error'
            }
    
    def _validate_framingham_parameters(self, age, total_cholesterol, hdl_cholesterol, systolic_bp) -> Dict[str, Any]:
        """Validate required Framingham Risk Score parameters"""
        missing = []

        if age is None or age < 30 or age > 74:
            missing.append('age (30-74 years)')
        if total_cholesterol is None or total_cholesterol < 100 or total_cholesterol > 405:
            missing.append('total_cholesterol (100-405 mg/dL)')
        if hdl_cholesterol is None or hdl_cholesterol < 20 or hdl_cholesterol > 100:
            missing.append('hdl_cholesterol (20-100 mg/dL)')
        if systolic_bp is None or systolic_bp < 90 or systolic_bp > 200:
            missing.append('systolic_blood_pressure (90-200 mmHg)')

        return {
            'valid': len(missing) == 0,
            'missing': missing
        }

    def _calculate_framingham_risk(self, age: int, gender: str, total_cholesterol: float,
                                 hdl_cholesterol: float, systolic_bp: float,
                                 on_bp_medication: bool, smoking: bool, diabetes: bool) -> float:
        """
        Calculate 10-year CVD risk using Framingham Risk Score formula

        Formula: Risk = 1 - S0^exp(ΣβX - mean_coefficient)
        Where S0 is baseline survival, β are coefficients, X are risk factor values
        """
        # Select gender-specific coefficients
        if gender.lower() in ['male', 'm', 'man']:
            coefficients = self.male_coefficients
            baseline_survival = self.male_baseline_survival
            mean_coefficient = self.male_mean_coefficient
        else:
            coefficients = self.female_coefficients
            baseline_survival = self.female_baseline_survival
            mean_coefficient = self.female_mean_coefficient

        # Calculate coefficient sum (ΣβX)
        coefficient_sum = 0.0

        # Age (natural log)
        coefficient_sum += coefficients['ln_age'] * math.log(age)

        # Total cholesterol (natural log)
        coefficient_sum += coefficients['ln_total_cholesterol'] * math.log(total_cholesterol)

        # HDL cholesterol (natural log)
        coefficient_sum += coefficients['ln_hdl'] * math.log(hdl_cholesterol)

        # Systolic blood pressure (natural log, treated vs untreated)
        if on_bp_medication:
            coefficient_sum += coefficients['ln_treated_sbp'] * math.log(systolic_bp)
        else:
            coefficient_sum += coefficients['ln_untreated_sbp'] * math.log(systolic_bp)

        # Smoking status (binary)
        if smoking:
            coefficient_sum += coefficients['smoking']

        # Diabetes status (binary)
        if diabetes:
            coefficient_sum += coefficients['diabetes']

        # Calculate 10-year risk
        # Risk = 1 - S0^exp(ΣβX - mean_coefficient)
        exponent = math.exp(coefficient_sum - mean_coefficient)
        risk = 1.0 - math.pow(baseline_survival, exponent)

        # Ensure risk is between 0 and 1
        return max(0.0, min(1.0, risk))
    
    def _apply_family_history_adjustment(self, base_risk: float, family_history: Dict[str, Any]) -> float:
        """
        Apply family history adjustments to Framingham risk
        Based on evidence from Lloyd-Jones et al. Circulation 2004
        """
        if not family_history:
            return base_risk

        # Calculate family history multiplier
        multiplier = 1.0

        for condition, has_condition in family_history.items():
            if has_condition and condition in self.family_history_multipliers:
                # Apply the highest single multiplier (not cumulative)
                condition_multiplier = self.family_history_multipliers[condition]
                multiplier = max(multiplier, condition_multiplier)

        # Apply multiplier to base risk, but cap at 90% (consistent with lifestyle cap)
        adjusted_risk = base_risk * multiplier
        return min(0.90, adjusted_risk)

    def _apply_lifestyle_adjustments(self, base_risk: float, lifestyle: Dict[str, Any]) -> float:
        """
        Apply evidence-based lifestyle adjustments to CVD risk
        Based on scientific literature on lifestyle factors and cardiovascular health
        """
        if not lifestyle:
            return base_risk

        risk_multiplier = 1.0

        # Exercise frequency adjustment (Strong evidence)
        # Regular exercise reduces CVD risk by 20-35%
        exercise_freq = lifestyle.get('exercise_frequency', 0)
        if exercise_freq >= 5:
            risk_multiplier *= 0.65  # 35% risk reduction for very active
        elif exercise_freq >= 3:
            risk_multiplier *= 0.75  # 25% risk reduction for moderately active
        elif exercise_freq >= 1:
            risk_multiplier *= 0.85  # 15% risk reduction for lightly active
        elif exercise_freq == 0:
            risk_multiplier *= 1.25  # 25% risk increase for sedentary

        # Sleep duration adjustment (Moderate evidence)
        # Both insufficient and excessive sleep increase CVD risk
        sleep_hours = lifestyle.get('sleep_hours', 8)
        if 7 <= sleep_hours <= 8:
            risk_multiplier *= 0.90  # 10% protective effect for optimal sleep
        elif sleep_hours < 6:
            risk_multiplier *= 1.25  # 25% increased risk for insufficient sleep
        elif sleep_hours > 9:
            risk_multiplier *= 1.15  # 15% increased risk for excessive sleep

        # Stress level adjustment (Moderate evidence)
        # Chronic stress increases CVD risk through multiple mechanisms
        stress_level = lifestyle.get('stress_level', 5)
        if stress_level <= 3:
            risk_multiplier *= 0.85  # 15% protective effect for low stress
        elif stress_level >= 8:
            risk_multiplier *= 1.30  # 30% increased risk for high stress
        elif stress_level >= 6:
            risk_multiplier *= 1.15  # 15% increased risk for moderate-high stress

        # Social support adjustment (Emerging evidence)
        # Strong social support has protective cardiovascular effects
        social_support = lifestyle.get('social_support', 5)
        if social_support >= 8:
            risk_multiplier *= 0.90  # 10% protective effect for strong social support
        elif social_support <= 3:
            risk_multiplier *= 1.10  # 10% increased risk for poor social support

        # Work-life balance adjustment (Emerging evidence)
        work_life_balance = lifestyle.get('work_life_balance', 5)
        if work_life_balance >= 8:
            risk_multiplier *= 0.90  # 10% protective effect for good work-life balance
        elif work_life_balance <= 3:
            risk_multiplier *= 1.10  # 10% increased risk for poor work-life balance

        # Apply lifestyle adjustments, but maintain reasonable bounds
        adjusted_risk = base_risk * risk_multiplier
        
        # Cap the risk between 0.5% and 95% (allow full range for lifestyle impact)
        return max(0.005, min(0.95, adjusted_risk))

    def _get_risk_category(self, risk_score: float) -> Dict[str, Any]:
        """Determine risk category based on 2019 AHA/ACC Guidelines"""
        for category_name, category_info in self.risk_categories.items():
            if risk_score < category_info['threshold']:
                return {
                    'name': category_name,
                    'label': category_info['label'],
                    'description': category_info['description'],
                    'threshold': category_info['threshold']
                }

        # Default to high risk if above all thresholds
        return {
            'name': 'high',
            'label': 'High',
            'description': '≥20% 10-year risk',
            'threshold': 0.20
        }
    
    def _identify_framingham_risk_factors(self, user_data: Dict[str, Any]) -> List[str]:
        """Identify specific risk factors based on Framingham parameters"""
        risk_factors = []

        age = user_data.get('age', 0)
        gender = user_data.get('gender', 'male')
        profile = user_data.get('profile', {})
        clinical = profile.get('clinical', {})
        lifestyle = profile.get('lifestyle', {})
        family_history = profile.get('family_history', {})

        # Age-related risks (gender-specific)
        if gender.lower() in ['male', 'm', 'man']:
            if age >= 45:
                risk_factors.append(f"Age {age} (men ≥45 years at increased risk)")
        else:
            if age >= 55:
                risk_factors.append(f"Age {age} (women ≥55 years at increased risk)")

        # Cholesterol levels
        total_chol = clinical.get('total_cholesterol')
        hdl_chol = clinical.get('hdl_cholesterol')

        if total_chol and total_chol >= 240:
            risk_factors.append(f"High total cholesterol ({total_chol} mg/dL, target <200)")
        elif total_chol and total_chol >= 200:
            risk_factors.append(f"Borderline high cholesterol ({total_chol} mg/dL)")

        if hdl_chol and hdl_chol < 40:
            risk_factors.append(f"Low HDL cholesterol ({hdl_chol} mg/dL, target ≥40)")

        # Blood pressure
        systolic_bp = clinical.get('systolic_blood_pressure')
        on_bp_meds = clinical.get('hypertension_treatment', False)

        if systolic_bp:
            if systolic_bp >= 140:
                risk_factors.append(f"High blood pressure ({systolic_bp} mmHg)")
            elif systolic_bp >= 130:
                risk_factors.append(f"Elevated blood pressure ({systolic_bp} mmHg)")

        if on_bp_meds:
            risk_factors.append("Currently on blood pressure medication")

        # Smoking
        if lifestyle.get('smoking', False):
            risk_factors.append("Current smoker (major modifiable risk factor)")

        # Diabetes
        if clinical.get('diabetes', False):
            risk_factors.append("Diabetes mellitus")

        # Family history
        for condition, has_condition in family_history.items():
            if has_condition and condition in self.family_history_multipliers:
                risk_factors.append(f"Family history of {condition.replace('_', ' ')}")

        return risk_factors
    
    def _calculate_nutrition_risk(self, nutrition_logs: List[Dict[str, Any]]) -> float:
        """Calculate nutrition-based risk from recent food logs"""
        if not nutrition_logs:
            return 0.5  # Default moderate risk if no data
        
        # Analyze recent nutrition logs (last 7 days)
        recent_logs = self._get_recent_nutrition_logs(nutrition_logs, days=7)
        
        if not recent_logs:
            return 0.5
        
        total_risk = 0.0
        log_count = len(recent_logs)
        
        for log in recent_logs:
            nutrition = log.get('nutrition', {})
            
            # High sodium risk
            sodium = nutrition.get('sodium', 0)
            if sodium > 600:  # mg per serving
                total_risk += 0.3
            elif sodium > 400:
                total_risk += 0.15
            
            # Saturated fat risk
            sat_fat = nutrition.get('saturated_fat', 0)
            if sat_fat > 5:  # g per serving
                total_risk += 0.25
            elif sat_fat > 3:
                total_risk += 0.1
            
            # Trans fat risk
            if nutrition.get('trans_fat', 0) > 0:
                total_risk += 0.4
            
            # Low fiber (lack of protective factor)
            if nutrition.get('fiber', 0) < 3:
                total_risk += 0.1
        
        return min(1.0, total_risk / log_count)
    
    def _get_recent_nutrition_logs(self, nutrition_logs: List[Dict[str, Any]], days: int = 7) -> List[Dict[str, Any]]:
        """Get nutrition logs from the last N days"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_logs = []
        
        for log in nutrition_logs:
            try:
                log_date = datetime.fromisoformat(log.get('logged_at', ''))
                if log_date >= cutoff_date:
                    recent_logs.append(log)
            except:
                continue  # Skip logs with invalid dates
        
        return recent_logs
    
    def _identify_risk_factors(self, user_data: Dict[str, Any], component_scores: Dict[str, float]) -> List[str]:
        """Identify specific risk factors present"""
        risk_factors = []
        
        age = user_data.get('age', 30)
        profile = user_data.get('profile', {})
        lifestyle = profile.get('lifestyle', {})
        family_history = profile.get('family_history', {})
        
        # Age-related risks
        if age >= 50:
            risk_factors.append(f"Age {age} (increased risk)")
        
        # Family history risks
        for condition in self.family_history_risks:
            if family_history.get(condition, False):
                risk_factors.append(f"Family history of {condition.replace('_', ' ')}")
        
        # Lifestyle risks
        if lifestyle.get('smoking', False):
            risk_factors.append("Current smoker")
        
        if lifestyle.get('exercise_frequency', 0) == 0:
            risk_factors.append("Sedentary lifestyle")
        
        if lifestyle.get('stress_level', 5) > 7:
            risk_factors.append("High stress levels")
        
        sleep_hours = lifestyle.get('sleep_hours', 8)
        if sleep_hours < 6:
            risk_factors.append("Insufficient sleep")
        elif sleep_hours > 9:
            risk_factors.append("Excessive sleep")
        
        # Nutrition risks (from recent analysis)
        if component_scores.get('nutrition_risk', 0) > 0.6:
            risk_factors.append("Poor dietary patterns")
        
        return risk_factors
    
    def _identify_protective_factors(self, user_data: Dict[str, Any]) -> List[str]:
        """Identify protective factors present"""
        protective_factors = []
        
        profile = user_data.get('profile', {})
        lifestyle = profile.get('lifestyle', {})
        
        # Exercise
        exercise_freq = lifestyle.get('exercise_frequency', 0)
        if exercise_freq >= 5:
            protective_factors.append("Very active lifestyle (5+ workouts/week)")
        elif exercise_freq >= 3:
            protective_factors.append("Active lifestyle (3+ workouts/week)")
        
        # Non-smoking
        if not lifestyle.get('smoking', False):
            protective_factors.append("Non-smoker")
        
        # Good sleep
        sleep_hours = lifestyle.get('sleep_hours', 8)
        if 7 <= sleep_hours <= 8:
            protective_factors.append("Healthy sleep patterns")
        
        # Low stress
        if lifestyle.get('stress_level', 5) <= 3:
            protective_factors.append("Low stress levels")
        
        # Young age
        age = user_data.get('age', 30)
        if age < 40:
            protective_factors.append("Young age")
        
        return protective_factors
    
    def _generate_clinical_recommendations(self, risk_score: float, risk_factors: List[str],
                                         user_data: Dict[str, Any]) -> List[str]:
        """
        Generate evidence-based clinical recommendations based on Framingham risk score
        Following 2019 AHA/ACC Primary Prevention Guidelines
        """
        recommendations = []

        profile = user_data.get('profile', {})
        clinical = profile.get('clinical', {})
        lifestyle = profile.get('lifestyle', {})

        # Risk-based recommendations
        risk_percentage = risk_score * 100

        if risk_score >= 0.20:  # High risk (≥20%)
            recommendations.append("HIGH RISK: Intensive lifestyle modifications recommended")
            recommendations.append("Consider statin therapy (discuss with healthcare provider)")
            recommendations.append("Consider aspirin therapy if bleeding risk is low")
            recommendations.append("Schedule regular monitoring every 3-6 months")

        elif risk_score >= 0.075:  # Intermediate risk (7.5-19.9%)
            recommendations.append("INTERMEDIATE RISK: Lifestyle modifications strongly recommended")
            recommendations.append("Consider statin therapy based on risk-benefit discussion")
            recommendations.append("Regular risk assessment every 6-12 months")

        elif risk_score >= 0.05:  # Borderline risk (5-7.4%)
            recommendations.append("BORDERLINE RISK: Focus on lifestyle modifications")
            recommendations.append("Consider additional risk factors (family history, CAC score)")
            recommendations.append("Annual risk reassessment recommended")

        else:  # Low risk (<5%)
            recommendations.append("LOW RISK: Continue healthy lifestyle practices")
            recommendations.append("Focus on primary prevention strategies")
            recommendations.append("Reassess risk every 2-3 years")

        # Specific modifiable risk factor recommendations
        if lifestyle.get('smoking', False):
            recommendations.append("🚭 PRIORITY: Smoking cessation - reduces risk by 50% within 1 year")

        total_chol = clinical.get('total_cholesterol')
        if total_chol and total_chol >= 200:
            recommendations.append("🥗 Dietary changes: Reduce saturated fat, increase fiber intake")

        hdl_chol = clinical.get('hdl_cholesterol')
        if hdl_chol and hdl_chol < 40:
            recommendations.append("🏃 Increase physical activity to raise HDL cholesterol")

        systolic_bp = clinical.get('systolic_blood_pressure')
        if systolic_bp and systolic_bp >= 130:
            recommendations.append("🩺 Blood pressure management: DASH diet, weight loss, exercise")

        if clinical.get('diabetes', False):
            recommendations.append("🩸 Diabetes management: Maintain HbA1c <7%, monitor regularly")

        # General lifestyle recommendations
        recommendations.append("🏃 Exercise: 150 minutes moderate activity or 75 minutes vigorous per week")
        recommendations.append("🥗 Diet: Mediterranean or DASH diet pattern")
        recommendations.append("⚖️ Weight management: Maintain BMI 18.5-24.9 kg/m²")

        # Medical follow-up
        recommendations.append("📋 Discuss these results with your healthcare provider")
        recommendations.append("📊 Consider additional testing if risk is borderline or intermediate")

        return recommendations

# Backward compatibility alias
CVDRiskCalculator = FraminghamRiskCalculator

# Convenience functions
def calculate_cvd_risk(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Main CVD risk calculation function - wrapper for the Framingham calculator"""
    calculator = FraminghamRiskCalculator()
    return calculator.calculate_cvd_risk(user_data)

def quick_framingham_assessment(age: int, gender: str, total_cholesterol: float, hdl_cholesterol: float,
                               systolic_bp: float, on_bp_medication: bool = False,
                               smoking: bool = False, diabetes: bool = False) -> Dict[str, Any]:
    """Quick Framingham risk assessment with clinical data"""
    user_data = {
        'age': age,
        'gender': gender,
        'profile': {
            'clinical': {
                'total_cholesterol': total_cholesterol,
                'hdl_cholesterol': hdl_cholesterol,
                'systolic_blood_pressure': systolic_bp,
                'hypertension_treatment': on_bp_medication,
                'diabetes': diabetes
            },
            'lifestyle': {
                'smoking': smoking
            }
        }
    }

    return calculate_cvd_risk(user_data)

# Legacy compatibility function
def quick_risk_assessment(age: int, smoking: bool = False, exercise_freq: int = 0, family_history: bool = False) -> Dict[str, Any]:
    """Legacy quick risk assessment - returns error message directing to use clinical data"""
    return {
        'success': False,
        'error': 'This function requires clinical data for Framingham Risk Score calculation. Use quick_framingham_assessment() instead.',
        'requires_clinical_data': True,
        'required_parameters': [
            'age (30-74 years)',
            'gender (male/female)',
            'total_cholesterol (mg/dL)',
            'hdl_cholesterol (mg/dL)',
            'systolic_blood_pressure (mmHg)',
            'hypertension_treatment (yes/no)',
            'smoking (yes/no)',
            'diabetes (yes/no)'
        ]
    }

def get_risk_trend(user_id: str, cvd_scores: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze risk trend over time"""
    if len(cvd_scores) < 2:
        return {'trend': 'insufficient_data', 'message': 'Need more data points to determine trend'}
    
    # Sort by date
    sorted_scores = sorted(cvd_scores, key=lambda x: x.get('calculated_at', ''))
    
    recent_score = sorted_scores[-1]['score']
    previous_score = sorted_scores[-2]['score']
    
    change = recent_score - previous_score
    
    if abs(change) < 0.05:
        trend = 'stable'
        message = 'Your CVD risk has remained stable'
    elif change > 0:
        trend = 'increasing'
        message = f'Your CVD risk has increased by {abs(change)*100:.1f}%'
    else:
        trend = 'decreasing'
        message = f'Your CVD risk has decreased by {abs(change)*100:.1f}%'
    
    return {
        'trend': trend,
        'change': change,
        'message': message,
        'current_score': recent_score,
        'previous_score': previous_score
    }
