import base64
import os
from typing import Dict, Any
import json

from backend.models.data_models import current_timestamp
from backend.infra.connection_openai import ConnectionOpenAI

class VisionAnalyzer:
    
    def __init__(self):
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.webp', '.gif']
        
        self.nutrition_prompt = """
        Analyze this food image for cardiovascular health assessment. If no food is detected in the image, return {"error": "No food detected in image"}.

        For valid food images, provide a comprehensive analysis with the following information:

        1. **Food identification**: Identify the specific food item and list its main ingredients
        2. **Nutritional content per serving** (provide numeric values):
        - Calories (kcal)
        - Saturated fat (grams, decimal allowed)
        - Trans fat (grams, decimal allowed)
        - Sodium (milligrams, whole number)
        - Fiber (grams, decimal allowed)
        - Sugar (grams, decimal allowed)
        - Protein (grams, decimal allowed)
        3. **Heart health score**: Rate from 1-10 (where 10 = most heart-healthy, 1 = least heart-healthy)
        4. **CVD risk factors**: List specific cardiovascular risk factors present in this food
        5. **Heart-healthy recommendations**: Suggest specific modifications or alternatives to improve cardiovascular health

        **REQUIRED JSON FORMAT** - Return exactly this structure:
        ```json
        {
        "food_name": "specific food name",
        "nutrition": {
            "calories": 0,
            "saturated_fat": 0.0,
            "trans_fat": 0.0,
            "sodium": 0,
            "fiber": 0.0,
            "sugar": 0.0,
            "protein": 0.0
        },
        "heart_health_score": 0,
        "cvd_risk_factors": ["list of specific risk factors"],
        "recommendations": ["list of specific heart-healthy suggestions"]
        }
        ```

        Ensure all numeric values are realistic estimates based on typical serving sizes. Be specific in risk factors and recommendations rather than generic advice.
        """
    
    def analyze_food(self, image_path: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Analyze food image for nutritional content and CVD risk assessment
        
        Args:
            image_path: Path to food image
            user_context: User profile for personalized recommendations
            
        Returns:
            Dict with food analysis, nutrition data, and CVD insights
        """
        try:
            # Validate image
            if not self._validate_image(image_path):
                return self._create_error_response(f"Invalid image: {image_path}")
            
            # Analyze with OpenAI Vision
            analysis_result = self._analyze_with_vision_api(image_path, user_context)
            
            if analysis_result['success']:
                # Extract structured nutrition data
                nutrition_data = self._extract_nutrition_data(analysis_result['raw_response'])
                
                # Check if nutrition_data contains an error (e.g., "no food detected")
                if 'error' in nutrition_data:
                    # Return the error directly from the AI response
                    return nutrition_data
                
                return {
                    'success': True,
                    'food_name': nutrition_data.get('food_name', 'Unknown food'),
                    'nutrition': nutrition_data.get('nutrition', {}),
                    'heart_health_score': nutrition_data.get('heart_health_score', 5),
                    'cvd_risk_factors': nutrition_data.get('cvd_risk_factors', []),
                    'recommendations': nutrition_data.get('recommendations', []),
                    'image_path': image_path,
                    'analysis_timestamp': current_timestamp(),
                    'raw_response': analysis_result['raw_response']
                }
            else:
                return analysis_result
                
        except Exception as e:
            return self._create_error_response(f"Food analysis error: {str(e)}")
    
    def _validate_image(self, image_path: str) -> bool:
        """Validate image file for processing"""
        if not os.path.exists(image_path):
            return False
        
        # Check file extension
        file_ext = os.path.splitext(image_path)[1].lower()
        if file_ext not in self.supported_formats:
            return False
        
        # Check if file is empty
        file_size = os.path.getsize(image_path)
        return file_size > 0
    
    def _analyze_with_vision_api(self, image_path: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze image using OpenAI Vision API"""
        try:
            # Get OpenAI client using ConnectionOpenAI
            client = ConnectionOpenAI.client()
            
            # Encode image to base64
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')
            
            # Build prompt with user context
            prompt = self.nutrition_prompt
            if user_context:
                prompt += f"\n\nUser context: Age {user_context.get('age', 'unknown')}"
                if 'profile' in user_context:
                    lifestyle = user_context['profile'].get('lifestyle', {})
                    prompt += f", Exercise: {lifestyle.get('exercise_frequency', 0)} times/week"
            
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                        }
                    ]
                }
            ]
            
            # Use the ConnectionOpenAI client
            response = client.chat.completions.create(
                model="gpt-4.1",
                messages=messages,
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            
            return {
                'success': True,
                'raw_response': response.choices[0].message.content
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Vision API error: {str(e)}"
            }
    
    def _extract_nutrition_data(self, raw_response: str) -> Dict[str, Any]:
        """Extract structured nutrition data from AI response"""
        try:
            # Try to parse JSON if present
            if '{' in raw_response and '}' in raw_response:
                json_start = raw_response.find('{')
                json_end = raw_response.rfind('}') + 1
                json_str = raw_response[json_start:json_end]
                parsed_json = json.loads(json_str)
                
                # Check if the JSON contains an error field
                if 'error' in parsed_json:
                    # Return the error directly - this will be handled by display_analysis_results
                    return parsed_json
                
                return parsed_json
        except json.JSONDecodeError:
            pass
        
        # Fallback: extract data using text parsing
        return self._parse_nutrition_text(raw_response)
    
    def _parse_nutrition_text(self, text: str) -> Dict[str, Any]:
        """Parse nutrition information from text response"""
        nutrition_data = {
            'food_name': 'Unknown food',
            'nutrition': {},
            'heart_health_score': 5,
            'cvd_risk_factors': [],
            'recommendations': []
        }
        
        lines = text.split('\n')
        for line in lines:
            line = line.strip().lower()
            
            # Extract food name
            if 'food:' in line or 'identified:' in line:
                nutrition_data['food_name'] = line.split(':')[1].strip()
            
            # Extract heart health score
            if 'heart health score' in line or 'score:' in line:
                import re
                score_match = re.search(r'(\d+)', line)
                if score_match:
                    nutrition_data['heart_health_score'] = int(score_match.group(1))
        
        return nutrition_data
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            'success': False,
            'error': error_message,
            'food_name': 'Unknown',
            'nutrition': {},
            'timestamp': current_timestamp()
        }

# Convenience functions
def analyze_food(image_path: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
    """Main food analysis function - wrapper for the class method"""
    analyzer = VisionAnalyzer()
    return analyzer.analyze_food(image_path, user_context)

def quick_food_scan(image_path: str) -> str:
    """Quick food identification that returns just the food name"""
    result = analyze_food(image_path)
    return result.get('food_name', 'Unknown food') if result.get('success') else 'Analysis failed'