Analyze this food image for cardiovascular health assessment. Provide:

1. Food identification (name and main ingredients)
2. Estimated nutritional content per serving:
   - Calories
   - Saturated fat (g)
   - Trans fat (g) 
   - Sodium (mg)
   - Fiber (g)
   - Sugar (g)
   - Protein (g)
3. Heart health score (1-10, where 10 is most heart-healthy)
4. CVD risk factors present
5. Heart-healthy alternatives or modifications

Format response as JSON with clear structure.

Example expected JSON format:
{
  "food_identification": {
    "name": "Grilled chicken salad",
    "main_ingredients": ["grilled chicken breast", "mixed greens", "tomatoes", "cucumber", "olive oil dressing"]
  },
  "nutritional_content": {
    "calories": 320,
    "saturated_fat": 2.5,
    "trans_fat": 0,
    "sodium": 450,
    "fiber": 6,
    "sugar": 8,
    "protein": 28
  },
  "heart_health_score": 8,
  "cvd_risk_factors": [
    "Moderate sodium content"
  ],
  "heart_healthy_alternatives": [
    "Use lemon juice instead of dressing to reduce sodium",
    "Add more vegetables for increased fiber",
    "Choose low-sodium seasonings"
  ]
}
