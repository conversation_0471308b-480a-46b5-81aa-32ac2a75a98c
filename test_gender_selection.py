#!/usr/bin/env python3
"""
Test script to verify gender selection is working correctly
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(__file__))

from backend.modules.cvd_score import calculate_cvd_risk

def test_gender_differences():
    """Test that gender selection affects CVD risk calculation"""
    
    # Base user data
    base_user_data = {
        'id': 'test_user',
        'age': 50,
        'profile': {
            'clinical': {
                'total_cholesterol': 200.0,
                'hdl_cholesterol': 45.0,
                'systolic_blood_pressure': 130.0,
                'hypertension_treatment': False,
                'diabetes': False
            },
            'lifestyle': {
                'smoking': False,
                'exercise_frequency': 3,
                'sleep_hours': 7,
                'stress_level': 5,
                'social_support': 6,
                'work_life_balance': 6
            },
            'family_history': {}
        }
    }
    
    print("🧪 Testing Gender Impact on CVD Risk Calculation")
    print("=" * 60)
    
    # Test Male
    male_user = base_user_data.copy()
    male_user['gender'] = 'male'
    
    result_male = calculate_cvd_risk(male_user)
    print(f"\n👨 MALE USER:")
    print(f"   Age: {male_user['age']} years")
    print(f"   Gender: {male_user['gender']}")
    print(f"   CVD Risk: {result_male['percentage']:.2f}%")
    print(f"   Risk Level: {result_male['risk_level']}")
    
    # Test Female
    female_user = base_user_data.copy()
    female_user['gender'] = 'female'
    
    result_female = calculate_cvd_risk(female_user)
    print(f"\n👩 FEMALE USER:")
    print(f"   Age: {female_user['age']} years")
    print(f"   Gender: {female_user['gender']}")
    print(f"   CVD Risk: {result_female['percentage']:.2f}%")
    print(f"   Risk Level: {result_female['risk_level']}")
    
    # Calculate difference
    gender_difference = result_male['percentage'] - result_female['percentage']
    
    print(f"\n📊 GENDER COMPARISON:")
    print(f"   Male Risk:     {result_male['percentage']:.2f}%")
    print(f"   Female Risk:   {result_female['percentage']:.2f}%")
    print(f"   Difference:    {gender_difference:+.2f}% (Male vs Female)")
    
    if gender_difference > 0:
        print(f"   ✅ Males have {gender_difference:.2f}% higher CVD risk")
    else:
        print(f"   ✅ Females have {abs(gender_difference):.2f}% higher CVD risk")
    
    print(f"\n💡 FRAMINGHAM COEFFICIENTS:")
    print(f"   Male Framingham:   {result_male['framingham_risk']:.4f}")
    print(f"   Female Framingham: {result_female['framingham_risk']:.4f}")
    
    # Test different ages to show how gender gap changes
    print(f"\n🎂 AGE COMPARISON:")
    ages = [40, 50, 60, 70]
    
    for age in ages:
        male_test = base_user_data.copy()
        male_test['age'] = age
        male_test['gender'] = 'male'
        
        female_test = base_user_data.copy()
        female_test['age'] = age
        female_test['gender'] = 'female'
        
        male_result = calculate_cvd_risk(male_test)
        female_result = calculate_cvd_risk(female_test)
        
        age_diff = male_result['percentage'] - female_result['percentage']
        
        print(f"   Age {age}: Male {male_result['percentage']:.1f}% vs Female {female_result['percentage']:.1f}% (Diff: {age_diff:+.1f}%)")

if __name__ == "__main__":
    test_gender_differences()
