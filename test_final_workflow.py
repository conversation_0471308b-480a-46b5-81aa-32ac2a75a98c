#!/usr/bin/env python3
"""
Test final integration - saving lifestyle and family history updates
"""

import sys
import os

# Add the project root to Python path  
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from backend.modules.crud import list_users, update_user
from backend.modules.cvd_score import FraminghamRiskCalculator

def test_save_and_recalculate():
    """Test saving lifestyle/family changes and recalculating"""
    print("🔄 TESTING SAVE AND RECALCULATE WORKFLOW")
    print("=" * 60)
    
    users = list_users()
    if not users:
        print("❌ No users found")
        return
    
    user = users[0]
    print(f"👤 Testing with: {user.get('name', 'Unknown')}")
    
    # Show current scores
    cvd_scores = user.get('cvd_scores', [])
    if cvd_scores:
        latest = cvd_scores[-1]
        print(f"📊 Current risk: {latest['score']:.1%} ({latest['risk_level']})")
        
        if 'factors' in latest and 'lifestyle_adjusted_risk' in latest['factors']:
            factors = latest['factors']
            print(f"   Framingham: {factors['framingham_risk']:.1%}")
            print(f"   Family: {factors['family_adjusted_risk']:.1%}")
            print(f"   Lifestyle: {factors['lifestyle_adjusted_risk']:.1%}")
    
    # Simulate user making GOOD lifestyle changes
    print(f"\n🏃‍♂️ SIMULATING GOOD LIFESTYLE CHANGES:")
    
    # Update lifestyle to good habits
    current_profile = user.get('profile', {})
    current_profile['lifestyle'] = {
        'exercise_frequency': 5,  # 5x per week - excellent
        'sleep_hours': 8,         # optimal sleep
        'stress_level': 3,        # low stress
        'social_support': 9,      # excellent social support
        'work_life_balance': 8    # good work-life balance
    }
    
    print(f"   Exercise: 1x/week → 5x/week")
    print(f"   Sleep: 6 hours → 8 hours")
    print(f"   Stress: 8/10 → 3/10")
    print(f"   Social Support: 3/10 → 9/10")
    print(f"   Work-Life Balance: 2/10 → 8/10")
    
    # Save changes
    update_success = update_user(user['id'], {'profile': current_profile})
    if update_success:
        print(f"✅ Lifestyle changes saved")
        
        # Recalculate risk
        calculator = FraminghamRiskCalculator()
        result = calculator.calculate_cvd_risk(user)
        
        if result.get('success'):
            print(f"\n📊 NEW CALCULATION AFTER LIFESTYLE IMPROVEMENT:")
            print(f"   New Risk: {result['score']:.1%} ({result['risk_level']})")
            
            # Check breakdown
            score_data = result['score_data']
            factors = score_data['factors']
            
            print(f"   Framingham Base: {factors['framingham_risk']:.1%}")
            print(f"   Family Adjusted: {factors['family_adjusted_risk']:.1%}")
            print(f"   Lifestyle Adjusted: {factors['lifestyle_adjusted_risk']:.1%}")
            
            # Calculate impact of lifestyle change
            old_lifestyle = cvd_scores[-1]['factors']['lifestyle_adjusted_risk']
            new_lifestyle = factors['lifestyle_adjusted_risk']
            improvement = (old_lifestyle - new_lifestyle) * 100
            
            print(f"\n📈 LIFESTYLE IMPACT:")
            print(f"   Before: {old_lifestyle:.1%}")
            print(f"   After: {new_lifestyle:.1%}")
            print(f"   Improvement: {improvement:+.1f}%")
            
            # Add new score to user
            cvd_scores.append(score_data)
            final_update = update_user(user['id'], {'cvd_scores': cvd_scores})
            
            if final_update:
                print(f"✅ New score saved to user profile")
                
                # Final verification - simulate what frontend would show
                print(f"\n🖥️ FRONTEND WOULD NOW SHOW:")
                print(f"   📈 Comprehensive CVD Risk: {result['score']:.1%}")
                print(f"   🟡 Risk Level: {result['risk_level']}")
                print(f"   📊 Breakdown:")
                print(f"      🔬 Framingham Base: {factors['framingham_risk']:.1%}")
                print(f"      🧬 After Family History: {factors['family_adjusted_risk']:.1%} ({(factors['family_adjusted_risk']-factors['framingham_risk'])*100:+.1f}%)")
                print(f"      🏃‍♂️ After Lifestyle: {factors['lifestyle_adjusted_risk']:.1%} ({(factors['lifestyle_adjusted_risk']-factors['family_adjusted_risk'])*100:+.1f}%)")
                print(f"   ✅ Includes Framingham + Lifestyle + Family History")
                
                return True
            else:
                print(f"❌ Failed to save new score")
        else:
            print(f"❌ Recalculation failed: {result.get('error')}")
    else:
        print(f"❌ Failed to save lifestyle changes")
    
    return False

if __name__ == "__main__":
    success = test_save_and_recalculate()
    if success:
        print(f"\n🎉 SUCCESS: Complete workflow working!")
        print(f"   ✅ Lifestyle factors affect risk score")
        print(f"   ✅ Family history affects risk score") 
        print(f"   ✅ Comprehensive risk display working")
        print(f"   ✅ Breakdown shows detailed impacts")
    else:
        print(f"\n❌ FAILURE: Workflow has issues")
