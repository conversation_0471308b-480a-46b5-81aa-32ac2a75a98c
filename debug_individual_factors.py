#!/usr/bin/env python3
"""
Debug individual lifestyle factors to see which one is causing the negative impact
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(__file__))

from backend.modules.cvd_score import FraminghamRiskCalculator

def debug_individual_factors():
    """Debug each lifestyle factor individually"""
    
    calculator = FraminghamRiskCalculator()
    
    # Base lifestyle (neutral)
    base_lifestyle = {
        'smoking': False,
        'exercise_frequency': 3,
        'sleep_hours': 7,
        'stress_level': 5,
        'social_support': 5,
        'work_life_balance': 5
    }
    
    # Worst lifestyle
    worst_lifestyle = {
        'smoking': True,
        'exercise_frequency': 0,      # Sedentary
        'sleep_hours': 4,             # Poor sleep
        'stress_level': 10,           # Maximum stress
        'social_support': 1,          # No social support
        'work_life_balance': 1        # Terrible work-life balance
    }
    
    print("🔍 DEBUGGING INDIVIDUAL LIFESTYLE FACTORS")
    print("=" * 60)
    
    base_risk = 0.50  # 50% starting risk for testing
    
    print(f"Base risk for testing: {base_risk*100:.1f}%")
    
    # Test base lifestyle
    base_adjusted = calculator._apply_lifestyle_adjustments(base_risk, base_lifestyle)
    print(f"Base lifestyle result: {base_adjusted*100:.1f}%")
    
    # Test worst lifestyle
    worst_adjusted = calculator._apply_lifestyle_adjustments(base_risk, worst_lifestyle)
    print(f"Worst lifestyle result: {worst_adjusted*100:.1f}%")
    
    print(f"\nShould worst be higher than base: {worst_adjusted > base_adjusted}")
    
    # Test each factor individually
    print(f"\n🧪 TESTING INDIVIDUAL FACTORS:")
    
    # Test exercise
    test_lifestyle = base_lifestyle.copy()
    test_lifestyle['exercise_frequency'] = 0  # Sedentary
    exercise_result = calculator._apply_lifestyle_adjustments(base_risk, test_lifestyle)
    print(f"Sedentary exercise: {exercise_result*100:.1f}% (vs base {base_adjusted*100:.1f}%)")
    
    # Test sleep
    test_lifestyle = base_lifestyle.copy()
    test_lifestyle['sleep_hours'] = 4  # Poor sleep
    sleep_result = calculator._apply_lifestyle_adjustments(base_risk, test_lifestyle)
    print(f"Poor sleep (4h): {sleep_result*100:.1f}% (vs base {base_adjusted*100:.1f}%)")
    
    # Test stress
    test_lifestyle = base_lifestyle.copy()
    test_lifestyle['stress_level'] = 10  # High stress
    stress_result = calculator._apply_lifestyle_adjustments(base_risk, test_lifestyle)
    print(f"High stress (10): {stress_result*100:.1f}% (vs base {base_adjusted*100:.1f}%)")
    
    # Test social support
    test_lifestyle = base_lifestyle.copy()
    test_lifestyle['social_support'] = 1  # Poor social support
    social_result = calculator._apply_lifestyle_adjustments(base_risk, test_lifestyle)
    print(f"Poor social (1): {social_result*100:.1f}% (vs base {base_adjusted*100:.1f}%)")
    
    # Test work-life balance
    test_lifestyle = base_lifestyle.copy()
    test_lifestyle['work_life_balance'] = 1  # Poor work-life balance
    work_result = calculator._apply_lifestyle_adjustments(base_risk, test_lifestyle)
    print(f"Poor work-life (1): {work_result*100:.1f}% (vs base {base_adjusted*100:.1f}%)")
    
    # Test smoking
    test_lifestyle = base_lifestyle.copy()
    test_lifestyle['smoking'] = True  # Smoking - this is handled in Framingham, not lifestyle adjustments
    smoking_result = calculator._apply_lifestyle_adjustments(base_risk, test_lifestyle)
    print(f"Smoking: {smoking_result*100:.1f}% (vs base {base_adjusted*100:.1f}%) - NOTE: Smoking handled in Framingham")
    
    # Manual calculation to verify
    print(f"\n🧮 MANUAL CALCULATION FOR WORST LIFESTYLE:")
    multiplier = 1.0
    print(f"Starting multiplier: {multiplier}")
    
    # Exercise: sedentary = 1.25
    multiplier *= 1.25
    print(f"After sedentary exercise (*1.25): {multiplier}")
    
    # Sleep: 4 hours = 1.25
    multiplier *= 1.25
    print(f"After poor sleep (*1.25): {multiplier}")
    
    # Stress: 10 = 1.30
    multiplier *= 1.30
    print(f"After high stress (*1.30): {multiplier}")
    
    # Social support: 1 = 1.10
    multiplier *= 1.10
    print(f"After poor social support (*1.10): {multiplier}")
    
    # Work-life balance: 1 = 1.10
    multiplier *= 1.10
    print(f"After poor work-life balance (*1.10): {multiplier}")
    
    manual_result = base_risk * multiplier
    manual_capped = max(0.005, min(0.85, manual_result))
    
    print(f"\nFinal multiplier: {multiplier}")
    print(f"Manual calculation: {base_risk} * {multiplier} = {manual_result} ({manual_result*100:.1f}%)")
    print(f"After capping to 85%: {manual_capped} ({manual_capped*100:.1f}%)")

if __name__ == "__main__":
    debug_individual_factors()
