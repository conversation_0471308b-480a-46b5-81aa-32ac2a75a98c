import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get project root directory (where config.py is located)
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# App Configuration
APP_NAME = os.getenv('APP_NAME', 'MIT_CVD_APP')
DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'

# Directory Configuration
DATA_DIR = os.path.join(PROJECT_ROOT, os.getenv('DATA_DIR', 'data'))
REPORTS_DIR = os.path.join(PROJECT_ROOT, os.getenv('REPORTS_DIR', 'reports'))
TEMP_DIR = os.path.join(PROJECT_ROOT, os.getenv('TEMP_DIR', 'temp'))
BACKEND_DIR = os.path.join(PROJECT_ROOT, 'backend')
FRONT_DIR = os.path.join(PROJECT_ROOT, 'front')

# Ensure directories exist
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(REPORTS_DIR, exist_ok=True)
os.makedirs(TEMP_DIR, exist_ok=True)

# File paths
USERS_DB_PATH = os.path.join(DATA_DIR, 'users.json')
SESSIONS_DB_PATH = os.path.join(DATA_DIR, 'sessions.json')
STREAKS_DB_PATH = os.path.join(DATA_DIR, 'streaks.json')
FOOD_HISTORY_PATH = os.path.join(DATA_DIR, 'food_analysis_history.json')

# Test file path
TEST_FILE_PATH = os.path.join(PROJECT_ROOT, 'teste.txt')

# CVD Risk Calculation - Now using Framingham Risk Score
# Coefficients are embedded in the FraminghamRiskCalculator class
# Based on D'Agostino RB Sr, et al. "General cardiovascular risk profile for use in primary care."
# Circulation. 2008;117(6):743-53.

# Risk Categories (2019 AHA/ACC Guidelines)
FRAMINGHAM_RISK_CATEGORIES = {
    'low': {'threshold': 0.05, 'label': 'Low', 'description': '<5% 10-year risk'},
    'borderline': {'threshold': 0.075, 'label': 'Borderline', 'description': '5-7.4% 10-year risk'},
    'intermediate': {'threshold': 0.20, 'label': 'Intermediate', 'description': '7.5-19.9% 10-year risk'},
    'high': {'threshold': 1.0, 'label': 'High', 'description': '≥20% 10-year risk'}
}

# Gamification Settings
STREAK_BONUS_MULTIPLIER = 1.1
MAX_DAILY_POINTS = 100
